# PickerAddress 城市地址选择器

一个功能完整、高性能的城市地址选择器组件，基于 CascadeSelection 组件封装，支持省市区三级联动选择。

## ✨ 功能特性

- 🏙️ **省市区三级联动** - 完整的中国城市数据，支持省市区三级选择
- 🔍 **智能搜索** - 支持城市名称搜索，快速定位目标城市
- 🔥 **热门城市** - 内置热门城市快速选择功能
- 🎨 **样式定制** - 丰富的主题和样式配置选项
- 📱 **响应式设计** - 完美适配移动端和桌面端
- ⚡ **高性能** - 基于 CascadeSelection 的性能优化
- 🛡️ **TypeScript 支持** - 完整的类型定义和智能提示
- 🔧 **程序化控制** - 支持程序化设置和清空地址
- 📡 **丰富事件** - 完整的事件回调支持

## 📦 安装使用

```typescript
// 1. 导入组件
import { PickerAddress, type AddressInfo } from '@/components/PickerAddress'

// 2. 使用组件
<PickerAddress
  v-model:value="selectedAddress"
  title="选择地址"
  @confirm="handleConfirm"
/>
```

## 🎯 基础用法

### 简单示例

```vue
<template>
  <BottomPopup v-model:visible="visible" height="70vh">
    <PickerAddress
      v-model:value="addressValue"
      title="选择地址"
      :show-footer="true"
      @confirm="handleConfirm"
      @cancel="visible = false"
    />
  </BottomPopup>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PickerAddress, type AddressInfo } from '@/components/PickerAddress'

const visible = ref(false)
const addressValue = ref<number[]>([])

const handleConfirm = (addressInfo: AddressInfo) => {
  console.log('选中的地址:', addressInfo)
  console.log('完整地址:', addressInfo.fullAddress)
  visible.value = false
}
</script>
```

## 📋 API 文档

### Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | `number[]` | `[]` | 当前选中的地址值（省市区代码数组） |
| defaultValue | `number[]` | `[]` | 默认选中的地址值 |
| title | `string` | `'选择地区'` | 组件标题 |
| placeholder | `string` | `'请选择'` | 占位文本 |
| emptyText | `string` | `'暂无数据'` | 空数据提示文本 |
| searchPlaceholder | `string` | `'搜索城市'` | 搜索框占位文本 |
| showSearch | `boolean` | `true` | 是否显示搜索功能 |
| showHotCities | `boolean` | `true` | 是否显示热门城市 |
| showClose | `boolean` | `false` | 是否显示关闭按钮 |
| showFooter | `boolean` | `true` | 是否显示底部按钮 |
| showActiveIndicator | `boolean` | `true` | 是否显示激活指示器 |
| height | `number \| string` | `'60vh'` | 组件高度 |
| cascadeHeight | `number \| string` | `'auto'` | 级联选择器高度 |
| itemHeight | `number` | `44` | 选项高度 |
| backgroundColor | `string` | `'#ffffff'` | 背景颜色 |
| activeColor | `string` | `'#007aff'` | 激活颜色 |
| textColor | `string` | `'#333333'` | 文本颜色 |
| borderColor | `string` | `'#e5e5e5'` | 边框颜色 |
| confirmText | `string` | `'确定'` | 确认按钮文本 |
| cancelText | `string` | `'取消'` | 取消按钮文本 |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:value | `(value: number[])` | 地址值更新时触发 |
| select | `(addressInfo: AddressInfo, level: number)` | 选择地址时触发 |
| change | `(addressInfo: AddressInfo)` | 地址改变时触发 |
| confirm | `(addressInfo: AddressInfo)` | 点击确认按钮时触发 |
| cancel | `()` | 点击取消按钮时触发 |
| close | `()` | 点击关闭按钮时触发 |
| search | `(keyword: string, results: CascadeItem[])` | 搜索时触发 |

### AddressInfo 接口

```typescript
interface AddressInfo {
  province: string        // 省份名称
  provinceCode: number    // 省份代码
  city: string           // 城市名称
  cityCode: number       // 城市代码
  district: string       // 区县名称
  districtCode: number   // 区县代码
  fullAddress: string    // 完整地址字符串
}
```

### 组件方法

通过 ref 可以调用以下方法：

```typescript
// 获取当前地址信息
const addressInfo = pickerRef.value.getAddressInfo()

// 设置地址
pickerRef.value.setAddress('北京市', '北京市市辖区', '东城区')

// 清空选择
pickerRef.value.clearSelection()

// 搜索城市
pickerRef.value.searchCity('北京')
```

## 🎨 样式定制

### 使用预设主题

```vue
<PickerAddress
  v-model:value="addressValue"
  active-color="#52c41a"
  border-color="#d9f7be"
  title="选择地址"
/>
```

### 使用预设配置

```typescript
import { pickerAddressUtils } from '@/components/PickerAddress'

// 创建绿色主题的完整模式配置
const config = pickerAddressUtils.createPreset('green', 'full')

// 在组件中使用
<PickerAddress v-bind="config" />
```

## 🔧 高级用法

### 不同使用模式

```vue
<!-- 简单模式（只显示级联选择器） -->
<PickerAddress
  :show-search="false"
  :show-hot-cities="false"
  height="400px"
/>

<!-- 搜索模式（突出搜索功能） -->
<PickerAddress
  :show-search="true"
  :show-hot-cities="false"
  height="60vh"
/>

<!-- 完整模式（所有功能） -->
<PickerAddress
  :show-search="true"
  :show-hot-cities="true"
  height="70vh"
/>
```

### 默认值设置

```vue
<PickerAddress
  :default-value="[110000, 110100, 110101]"
  title="选择地址"
/>
```

### 程序化控制

```vue
<template>
  <PickerAddress
    ref="pickerRef"
    v-model:value="addressValue"
  />
  <button @click="setBeijing">设置北京</button>
  <button @click="clearAddress">清空</button>
</template>

<script setup lang="ts">
const pickerRef = ref()

const setBeijing = () => {
  pickerRef.value?.setAddress('北京市', '北京市市辖区', '东城区')
}

const clearAddress = () => {
  pickerRef.value?.clearSelection()
}
</script>
```

### 事件监听

```vue
<PickerAddress
  @select="handleSelect"
  @change="handleChange"
  @search="handleSearch"
  @confirm="handleConfirm"
/>

<script setup lang="ts">
const handleSelect = (addressInfo: AddressInfo, level: number) => {
  console.log(`选择了第${level}级:`, addressInfo)
}

const handleChange = (addressInfo: AddressInfo) => {
  console.log('地址改变:', addressInfo.fullAddress)
}

const handleSearch = (keyword: string, results: any[]) => {
  console.log(`搜索"${keyword}"，找到${results.length}个结果`)
}

const handleConfirm = (addressInfo: AddressInfo) => {
  console.log('确认选择:', addressInfo)
}
</script>
```

## 🛠️ 工具函数

### 地址格式化

```typescript
import { formatAddress, type AddressInfo } from '@/components/PickerAddress'

const addressInfo: AddressInfo = {
  province: '北京市',
  city: '北京市市辖区',
  district: '东城区',
  // ...
}

// 格式化地址
const formatted = formatAddress(addressInfo, {
  showProvince: true,
  showCity: false,
  showDistrict: true,
  separator: '-'
})
// 结果: "北京市-东城区"
```

### 地址查找

```typescript
import { findValuesByAddress } from '@/components/PickerAddress'

// 根据地址名称查找代码
const values = findValuesByAddress(cityData, '北京市', '北京市市辖区', '东城区')
// 结果: [110000, 110100, 110101]
```

## 📱 响应式适配

组件内置了响应式设计，在不同屏幕尺寸下会自动调整：

- **桌面端**: 完整显示所有功能
- **移动端**: 优化触摸交互，调整间距和字体大小

## ⚡ 性能优化

- **按需加载**: 城市数据按需动态加载
- **搜索防抖**: 避免频繁搜索请求
- **虚拟滚动**: 大数据量时的性能优化
- **缓存机制**: 热门城市和搜索结果缓存

## 🔄 数据格式

组件使用标准的中国行政区划数据，支持：
- 34个省级行政区
- 334个地级行政区
- 2851个县级行政区

数据格式符合国家标准，定期更新。

## 📄 许可证

MIT License
