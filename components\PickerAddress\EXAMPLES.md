# PickerAddress 使用示例

## 🚀 快速开始

### 1. 基础使用

```vue
<template>
  <view>
    <button @click="showPicker">选择地址</button>
    <text v-if="selectedAddress">{{ selectedAddress }}</text>
    
    <BottomPopup v-model:visible="visible" height="70vh">
      <PickerAddress
        v-model:value="addressValue"
        title="选择地址"
        @confirm="handleConfirm"
        @cancel="visible = false"
      />
    </BottomPopup>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PickerAddress, type AddressInfo } from '@/components/PickerAddress'
import { BottomPopup } from '@/components/BottomPopup'

const visible = ref(false)
const addressValue = ref<number[]>([])
const selectedAddress = ref('')

const showPicker = () => {
  visible.value = true
}

const handleConfirm = (addressInfo: AddressInfo) => {
  selectedAddress.value = addressInfo.fullAddress
  visible.value = false
}
</script>
```

### 2. 带默认值

```vue
<template>
  <PickerAddress
    :default-value="[110000, 110100, 110101]"
    title="选择地址"
    @confirm="handleConfirm"
  />
</template>

<script setup lang="ts">
// 默认选中：北京市 > 北京市市辖区 > 东城区
const handleConfirm = (addressInfo: AddressInfo) => {
  console.log('选中地址:', addressInfo.fullAddress)
}
</script>
```

### 3. 简化模式

```vue
<template>
  <!-- 只显示级联选择器，不显示搜索和热门城市 -->
  <PickerAddress
    :show-search="false"
    :show-hot-cities="false"
    height="400px"
    title="选择地址"
  />
</template>
```

### 4. 搜索模式

```vue
<template>
  <!-- 突出搜索功能 -->
  <PickerAddress
    :show-search="true"
    :show-hot-cities="false"
    search-placeholder="输入城市名称搜索"
    title="搜索城市"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
const handleSearch = (keyword: string, results: any[]) => {
  console.log(`搜索"${keyword}"，找到${results.length}个结果`)
}
</script>
```

## 🎨 样式定制示例

### 1. 自定义颜色主题

```vue
<template>
  <!-- 绿色主题 -->
  <PickerAddress
    active-color="#52c41a"
    border-color="#d9f7be"
    title="选择地址"
  />
  
  <!-- 橙色主题 -->
  <PickerAddress
    active-color="#fa8c16"
    border-color="#ffd591"
    title="选择地址"
  />
  
  <!-- 紫色主题 -->
  <PickerAddress
    active-color="#722ed1"
    border-color="#d3adf7"
    title="选择地址"
  />
</template>
```

### 2. 使用预设主题

```vue
<template>
  <PickerAddress v-bind="greenTheme" />
</template>

<script setup lang="ts">
import { pickerAddressUtils } from '@/components/PickerAddress'

// 创建绿色主题的完整模式配置
const greenTheme = pickerAddressUtils.createPreset('green', 'full')
</script>
```

### 3. 自定义尺寸

```vue
<template>
  <!-- 移动端优化 -->
  <PickerAddress
    height="80vh"
    :item-height="48"
    title="选择地址"
  />
  
  <!-- 紧凑模式 -->
  <PickerAddress
    height="300px"
    :item-height="36"
    title="选择地址"
  />
</template>
```

## 🔧 高级功能示例

### 1. 程序化控制

```vue
<template>
  <view>
    <PickerAddress
      ref="pickerRef"
      v-model:value="addressValue"
      title="程序化控制"
    />
    
    <view class="controls">
      <button @click="setBeijing">设置北京</button>
      <button @click="setShanghai">设置上海</button>
      <button @click="setGuangzhou">设置广州</button>
      <button @click="clearAddress">清空</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const pickerRef = ref()
const addressValue = ref<number[]>([])

const setBeijing = () => {
  pickerRef.value?.setAddress('北京市', '北京市市辖区', '东城区')
}

const setShanghai = () => {
  pickerRef.value?.setAddress('上海市', '上海市市辖区', '黄浦区')
}

const setGuangzhou = () => {
  pickerRef.value?.setAddress('广东省', '广州市', '越秀区')
}

const clearAddress = () => {
  pickerRef.value?.clearSelection()
}
</script>
```

### 2. 完整事件监听

```vue
<template>
  <view>
    <PickerAddress
      v-model:value="addressValue"
      title="事件监听示例"
      @select="handleSelect"
      @change="handleChange"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      @close="handleClose"
      @search="handleSearch"
    />
    
    <!-- 事件日志 -->
    <scroll-view class="event-logs">
      <view v-for="(log, index) in eventLogs" :key="index" class="log-item">
        <text class="log-time">{{ log.time }}</text>
        <text class="log-event">{{ log.event }}</text>
        <text class="log-data">{{ log.data }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { AddressInfo } from '@/components/PickerAddress'

const addressValue = ref<number[]>([])
const eventLogs = ref<Array<{time: string, event: string, data: string}>>([])

const addLog = (event: string, data: any) => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.push({
    time,
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : String(data)
  })
}

const handleSelect = (addressInfo: AddressInfo, level: number) => {
  addLog('select', `Level ${level}: ${addressInfo.fullAddress}`)
}

const handleChange = (addressInfo: AddressInfo) => {
  addLog('change', addressInfo.fullAddress)
}

const handleConfirm = (addressInfo: AddressInfo) => {
  addLog('confirm', addressInfo.fullAddress)
}

const handleCancel = () => {
  addLog('cancel', 'User cancelled')
}

const handleClose = () => {
  addLog('close', 'Picker closed')
}

const handleSearch = (keyword: string, results: any[]) => {
  addLog('search', `"${keyword}" -> ${results.length} results`)
}
</script>
```

### 3. 表单集成

```vue
<template>
  <form @submit="handleSubmit">
    <view class="form-item">
      <label>姓名:</label>
      <input v-model="form.name" placeholder="请输入姓名" />
    </view>
    
    <view class="form-item">
      <label>地址:</label>
      <view class="address-input" @click="showAddressPicker">
        <text v-if="form.address">{{ form.address }}</text>
        <text v-else class="placeholder">请选择地址</text>
      </view>
    </view>
    
    <button type="submit">提交</button>
    
    <BottomPopup v-model:visible="addressVisible" height="70vh">
      <PickerAddress
        v-model:value="addressValue"
        title="选择地址"
        @confirm="handleAddressConfirm"
        @cancel="addressVisible = false"
      />
    </BottomPopup>
  </form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { AddressInfo } from '@/components/PickerAddress'

const form = reactive({
  name: '',
  address: '',
  addressCodes: [] as number[]
})

const addressVisible = ref(false)
const addressValue = ref<number[]>([])

const showAddressPicker = () => {
  addressValue.value = [...form.addressCodes]
  addressVisible.value = true
}

const handleAddressConfirm = (addressInfo: AddressInfo) => {
  form.address = addressInfo.fullAddress
  form.addressCodes = addressValue.value
  addressVisible.value = false
}

const handleSubmit = (e: Event) => {
  e.preventDefault()
  console.log('提交表单:', form)
}
</script>
```

## 🛠️ 工具函数使用示例

### 1. 地址格式化

```typescript
import { formatAddress, getAddressInfo } from '@/components/PickerAddress'

// 假设有地址数据
const addressInfo = getAddressInfo(cityData, [110000, 110100, 110101])

// 不同格式化方式
const full = formatAddress(addressInfo, {
  showProvince: true,
  showCity: true,
  showDistrict: true
})
// 结果: "北京市北京市市辖区东城区"

const simple = formatAddress(addressInfo, {
  showProvince: true,
  showCity: false,
  showDistrict: true,
  separator: ' '
})
// 结果: "北京市 东城区"

const withSeparator = formatAddress(addressInfo, {
  separator: '-'
})
// 结果: "北京市-北京市市辖区-东城区"
```

### 2. 地址查找和验证

```typescript
import { 
  findValuesByAddress, 
  pickerAddressUtils,
  getAddressInfo 
} from '@/components/PickerAddress'

// 根据地址名称查找代码
const codes = findValuesByAddress(cityData, '北京市', '北京市市辖区', '东城区')
console.log(codes) // [110000, 110100, 110101]

// 验证地址值
const isValid = pickerAddressUtils.validateAddressValues([110000, 110100, 110101])
console.log(isValid) // true

// 获取地址层级
const addressInfo = getAddressInfo(cityData, [110000, 110100, 110101])
const level = pickerAddressUtils.getAddressLevel(addressInfo)
console.log(level) // 3

// 检查是否为完整地址
const isComplete = pickerAddressUtils.isCompleteAddress(addressInfo)
console.log(isComplete) // true
```

### 3. 地址比较

```typescript
import { pickerAddressUtils, getAddressInfo } from '@/components/PickerAddress'

const address1 = getAddressInfo(cityData, [110000, 110100, 110101])
const address2 = getAddressInfo(cityData, [110000, 110100, 110102])

// 比较两个地址是否相同
const isSame = pickerAddressUtils.compareAddress(address1, address2)
console.log(isSame) // false（不同区县）
```

## 📱 移动端优化示例

```vue
<template>
  <!-- 移动端优化配置 -->
  <PickerAddress
    v-bind="mobileConfig"
    title="选择地址"
  />
</template>

<script setup lang="ts">
import { pickerAddressUtils } from '@/components/PickerAddress'

// 使用移动端预设配置
const mobileConfig = pickerAddressUtils.createPreset('default', 'mobile')

// 或者手动配置
const customMobileConfig = {
  height: '80vh',
  itemHeight: 48,
  showSearch: true,
  showHotCities: true,
  activeColor: '#007aff'
}
</script>
```

## 🎯 实际应用场景

### 1. 电商收货地址

```vue
<template>
  <view class="address-form">
    <view class="form-row">
      <PickerAddress
        v-model:value="shippingAddress.codes"
        title="选择收货地址"
        @confirm="handleAddressSelect"
      />
    </view>
    
    <view class="form-row">
      <input 
        v-model="shippingAddress.detail" 
        placeholder="详细地址（街道、门牌号等）"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import type { AddressInfo } from '@/components/PickerAddress'

const shippingAddress = reactive({
  codes: [] as number[],
  region: '',
  detail: ''
})

const handleAddressSelect = (addressInfo: AddressInfo) => {
  shippingAddress.region = addressInfo.fullAddress
}
</script>
```

### 2. 用户注册地址选择

```vue
<template>
  <view class="register-form">
    <input v-model="userInfo.name" placeholder="姓名" />
    <input v-model="userInfo.phone" placeholder="手机号" />
    
    <view class="address-selector" @click="showAddressPicker">
      <text v-if="userInfo.address">{{ userInfo.address }}</text>
      <text v-else class="placeholder">选择所在地区</text>
    </view>
    
    <BottomPopup v-model:visible="addressVisible" height="70vh">
      <PickerAddress
        title="选择所在地区"
        @confirm="handleAddressConfirm"
        @cancel="addressVisible = false"
      />
    </BottomPopup>
  </view>
</template>
```

这些示例展示了 PickerAddress 组件在各种场景下的使用方法，可以根据具体需求进行调整和定制。
