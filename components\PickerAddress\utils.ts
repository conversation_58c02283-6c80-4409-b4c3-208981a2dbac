/**
 * 城市数据处理工具
 */

import type { CascadeItem } from '@/components/CascadeSelection'

// 原始城市数据接口
export interface RawCityItem {
  value: number
  text: string
  level: number
  pid: number
  children: RawCityItem[]
}

// 地址信息接口
export interface AddressInfo {
  province: string
  provinceCode: number
  city: string
  cityCode: number
  district: string
  districtCode: number
  fullAddress: string
}

/**
 * 将原始城市数据转换为级联选择器数据格式
 */
export function transformCityData(rawData: RawCityItem[]): CascadeItem[] {
  return rawData.map(province => ({
    label: province.text,
    value: province.value,
    level: province.level,
    pid: province.pid,
    children: province.children?.map(city => ({
      label: city.text,
      value: city.value,
      level: city.level,
      pid: city.pid,
      children: city.children?.map(district => ({
        label: district.text,
        value: district.value,
        level: district.level,
        pid: district.pid,
        children: []
      })) || []
    })) || []
  }))
}

/**
 * 根据值数组查找对应的地址信息
 */
export function getAddressInfo(
  cityData: CascadeItem[], 
  values: number[]
): AddressInfo | null {
  if (!values || values.length === 0) return null

  let province: CascadeItem | undefined
  let city: CascadeItem | undefined
  let district: CascadeItem | undefined

  // 查找省份
  if (values[0]) {
    province = cityData.find(item => item.value === values[0])
  }

  // 查找城市
  if (values[1] && province?.children) {
    city = province.children.find(item => item.value === values[1])
  }

  // 查找区县
  if (values[2] && city?.children) {
    district = city.children.find(item => item.value === values[2])
  }

  if (!province) return null

  const addressInfo: AddressInfo = {
    province: province.label || '',
    provinceCode: province.value as number,
    city: city?.label || '',
    cityCode: (city?.value as number) || 0,
    district: district?.label || '',
    districtCode: (district?.value as number) || 0,
    fullAddress: ''
  }

  // 构建完整地址
  const addressParts = [addressInfo.province]
  if (addressInfo.city && addressInfo.city !== addressInfo.province) {
    addressParts.push(addressInfo.city)
  }
  if (addressInfo.district) {
    addressParts.push(addressInfo.district)
  }
  addressInfo.fullAddress = addressParts.join('')

  return addressInfo
}

/**
 * 根据地址名称查找对应的值数组
 */
export function findValuesByAddress(
  cityData: CascadeItem[],
  provinceName?: string,
  cityName?: string,
  districtName?: string
): number[] {
  const values: number[] = []

  // 查找省份
  const province = cityData.find(item => 
    item.label === provinceName || item.label?.includes(provinceName || '')
  )
  if (!province) return values
  values.push(province.value as number)

  if (!cityName) return values

  // 查找城市
  const city = province.children?.find(item => 
    item.label === cityName || item.label?.includes(cityName)
  )
  if (!city) return values
  values.push(city.value as number)

  if (!districtName) return values

  // 查找区县
  const district = city.children?.find(item => 
    item.label === districtName || item.label?.includes(districtName)
  )
  if (!district) return values
  values.push(district.value as number)

  return values
}

/**
 * 获取热门城市列表
 */
export function getHotCities(cityData: CascadeItem[]): CascadeItem[] {
  const hotCityNames = [
    '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市',
    '武汉市', '成都市', '西安市', '重庆市', '天津市', '苏州市',
    '长沙市', '郑州市', '青岛市', '大连市', '宁波市', '厦门市'
  ]

  const hotCities: CascadeItem[] = []

  cityData.forEach(province => {
    province.children?.forEach(city => {
      if (hotCityNames.includes(city.label || '')) {
        hotCities.push({
          ...city,
          provinceName: province.label
        })
      }
    })
  })

  return hotCities
}

/**
 * 搜索城市
 */
export function searchCities(
  cityData: CascadeItem[], 
  keyword: string
): CascadeItem[] {
  if (!keyword.trim()) return []

  const results: CascadeItem[] = []
  const lowerKeyword = keyword.toLowerCase()

  cityData.forEach(province => {
    // 搜索省份
    if (province.label?.toLowerCase().includes(lowerKeyword)) {
      results.push({
        ...province,
        searchType: 'province'
      })
    }

    // 搜索城市
    province.children?.forEach(city => {
      if (city.label?.toLowerCase().includes(lowerKeyword)) {
        results.push({
          ...city,
          provinceName: province.label,
          searchType: 'city'
        })
      }

      // 搜索区县
      city.children?.forEach(district => {
        if (district.label?.toLowerCase().includes(lowerKeyword)) {
          results.push({
            ...district,
            provinceName: province.label,
            cityName: city.label,
            searchType: 'district'
          })
        }
      })
    })
  })

  return results.slice(0, 50) // 限制搜索结果数量
}

/**
 * 验证地址数据完整性
 */
export function validateCityData(data: any[]): boolean {
  if (!Array.isArray(data) || data.length === 0) return false

  return data.every(province => {
    if (!province.text || !province.value || province.level !== 1) return false
    
    if (province.children && Array.isArray(province.children)) {
      return province.children.every((city: any) => {
        if (!city.text || !city.value || city.level !== 2) return false
        
        if (city.children && Array.isArray(city.children)) {
          return city.children.every((district: any) => {
            return district.text && district.value && district.level === 3
          })
        }
        
        return true
      })
    }
    
    return true
  })
}

/**
 * 格式化地址显示
 */
export function formatAddress(
  addressInfo: AddressInfo,
  options: {
    showProvince?: boolean
    showCity?: boolean
    showDistrict?: boolean
    separator?: string
  } = {}
): string {
  const {
    showProvince = true,
    showCity = true,
    showDistrict = true,
    separator = ''
  } = options

  const parts: string[] = []

  if (showProvince && addressInfo.province) {
    parts.push(addressInfo.province)
  }

  if (showCity && addressInfo.city && addressInfo.city !== addressInfo.province) {
    parts.push(addressInfo.city)
  }

  if (showDistrict && addressInfo.district) {
    parts.push(addressInfo.district)
  }

  return parts.join(separator)
}

/**
 * 获取省份列表
 */
export function getProvinces(cityData: CascadeItem[]): CascadeItem[] {
  return cityData.map(province => ({
    label: province.label,
    value: province.value,
    level: province.level,
    pid: province.pid
  }))
}

/**
 * 根据省份获取城市列表
 */
export function getCitiesByProvince(
  cityData: CascadeItem[], 
  provinceCode: number
): CascadeItem[] {
  const province = cityData.find(item => item.value === provinceCode)
  return province?.children || []
}

/**
 * 根据城市获取区县列表
 */
export function getDistrictsByCity(
  cityData: CascadeItem[], 
  provinceCode: number, 
  cityCode: number
): CascadeItem[] {
  const province = cityData.find(item => item.value === provinceCode)
  const city = province?.children?.find(item => item.value === cityCode)
  return city?.children || []
}
