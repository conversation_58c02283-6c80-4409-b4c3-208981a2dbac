/**
 * 城市地址选择器组件
 * 
 * 功能特性：
 * - 基于 CascadeSelection 组件的城市选择器
 * - 支持省市区三级联动选择
 * - 内置搜索功能，支持城市名称搜索
 * - 热门城市快速选择
 * - 完整的地址信息返回
 * - 支持默认值设置和程序化控制
 * - 响应式设计，适配移动端
 * - TypeScript 支持
 */

import PickerAddress from './PickerAddress.vue'
import * as utils from './utils'

// 导出工具函数
export const {
  transformCityData,
  getAddressInfo,
  findValuesByAddress,
  getHotCities,
  searchCities,
  validateCityData,
  formatAddress,
  getProvinces,
  getCitiesByProvince,
  getDistrictsByCity
} = utils

// 导出类型
export type {
  RawCityItem,
  AddressInfo
} from './utils'

// 预设配置
export const pickerAddressPresets = {
  // 默认配置
  defaults: {
    title: '选择地区',
    placeholder: '请选择',
    searchPlaceholder: '搜索城市',
    showSearch: true,
    showHotCities: true,
    showFooter: true,
    height: '60vh',
    activeColor: '#007aff',
    backgroundColor: '#ffffff'
  },

  // 样式主题
  themes: {
    // 默认主题
    default: {
      activeColor: '#007aff',
      backgroundColor: '#ffffff',
      textColor: '#333333',
      borderColor: '#e5e5e5'
    },
    
    // 绿色主题
    green: {
      activeColor: '#52c41a',
      backgroundColor: '#ffffff',
      textColor: '#333333',
      borderColor: '#d9f7be'
    },
    
    // 橙色主题
    orange: {
      activeColor: '#fa8c16',
      backgroundColor: '#ffffff',
      textColor: '#333333',
      borderColor: '#ffd591'
    },
    
    // 紫色主题
    purple: {
      activeColor: '#722ed1',
      backgroundColor: '#ffffff',
      textColor: '#333333',
      borderColor: '#d3adf7'
    },
    
    // 暗色主题
    dark: {
      activeColor: '#1890ff',
      backgroundColor: '#1f1f1f',
      textColor: '#ffffff',
      borderColor: '#434343'
    }
  },

  // 使用场景配置
  scenarios: {
    // 简单选择（只显示级联选择器）
    simple: {
      showSearch: false,
      showHotCities: false,
      height: '400px'
    },
    
    // 搜索模式（突出搜索功能）
    search: {
      showSearch: true,
      showHotCities: false,
      height: '60vh'
    },
    
    // 完整模式（所有功能）
    full: {
      showSearch: true,
      showHotCities: true,
      height: '70vh'
    },
    
    // 移动端优化
    mobile: {
      showSearch: true,
      showHotCities: true,
      height: '80vh',
      itemHeight: 48
    }
  }
}

// 工具函数
export const pickerAddressUtils = {
  /**
   * 创建预设配置
   */
  createPreset(
    theme: keyof typeof pickerAddressPresets.themes = 'default',
    scenario: keyof typeof pickerAddressPresets.scenarios = 'full'
  ) {
    return {
      ...pickerAddressPresets.defaults,
      ...pickerAddressPresets.themes[theme],
      ...pickerAddressPresets.scenarios[scenario]
    }
  },

  /**
   * 验证地址选择值
   */
  validateAddressValues(values: number[]): boolean {
    if (!Array.isArray(values)) return false
    if (values.length === 0) return false
    if (values.length > 3) return false
    return values.every(value => typeof value === 'number' && value > 0)
  },

  /**
   * 格式化地址为字符串
   */
  formatAddressString(
    addressInfo: utils.AddressInfo | null,
    options: {
      showProvince?: boolean
      showCity?: boolean
      showDistrict?: boolean
      separator?: string
    } = {}
  ): string {
    if (!addressInfo) return ''
    return utils.formatAddress(addressInfo, options)
  },

  /**
   * 比较两个地址是否相同
   */
  compareAddress(
    address1: utils.AddressInfo | null,
    address2: utils.AddressInfo | null
  ): boolean {
    if (!address1 || !address2) return false
    return (
      address1.provinceCode === address2.provinceCode &&
      address1.cityCode === address2.cityCode &&
      address1.districtCode === address2.districtCode
    )
  },

  /**
   * 获取地址层级
   */
  getAddressLevel(addressInfo: utils.AddressInfo | null): number {
    if (!addressInfo) return 0
    if (addressInfo.districtCode) return 3
    if (addressInfo.cityCode) return 2
    if (addressInfo.provinceCode) return 1
    return 0
  },

  /**
   * 检查地址是否完整（包含省市区）
   */
  isCompleteAddress(addressInfo: utils.AddressInfo | null): boolean {
    return this.getAddressLevel(addressInfo) === 3
  }
}

// 常量定义
export const PICKER_ADDRESS_CONSTANTS = {
  // 最大搜索结果数量
  MAX_SEARCH_RESULTS: 50,
  
  // 热门城市数量
  HOT_CITIES_COUNT: 18,
  
  // 搜索防抖延迟
  SEARCH_DEBOUNCE_DELAY: 300,
  
  // 地址层级
  ADDRESS_LEVELS: {
    PROVINCE: 1,
    CITY: 2,
    DISTRICT: 3
  } as const,
  
  // 事件名称
  EVENTS: {
    SELECT: 'select',
    CHANGE: 'change',
    CONFIRM: 'confirm',
    CANCEL: 'cancel',
    CLOSE: 'close',
    SEARCH: 'search'
  } as const
}

// 组件导出
export { PickerAddress }
export default PickerAddress
