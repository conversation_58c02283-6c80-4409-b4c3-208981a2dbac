<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PickerAddress 组件测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .test-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #333;
    }
    
    .test-item {
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #e5e5e5;
      border-radius: 6px;
    }
    
    .test-label {
      font-weight: 500;
      margin-bottom: 8px;
      color: #666;
    }
    
    .test-result {
      margin-top: 8px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      white-space: pre-wrap;
    }
    
    .status {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }
    
    .status.success {
      background-color: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .status.warning {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .log-container {
      max-height: 300px;
      overflow-y: auto;
      background: #1e1e1e;
      color: #d4d4d4;
      padding: 12px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
    }
    
    .log-entry {
      margin-bottom: 4px;
    }
    
    .log-error {
      color: #f48771;
    }
    
    .log-warn {
      color: #dcdcaa;
    }
    
    .log-info {
      color: #9cdcfe;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>PickerAddress 组件修复测试</h1>
    
    <div class="test-section">
      <div class="section-title">🔧 修复内容</div>
      <div class="test-item">
        <div class="test-label">主要修复点:</div>
        <ul>
          <li>✅ 增强了数据加载的错误处理和调试信息</li>
          <li>✅ 添加了备用数据加载方法</li>
          <li>✅ 改进了数据验证逻辑</li>
          <li>✅ 修复了TypeScript类型错误</li>
          <li>✅ 添加了详细的控制台日志</li>
          <li>✅ 提供了最小测试数据作为后备</li>
        </ul>
      </div>
    </div>
    
    <div class="test-section">
      <div class="section-title">🧪 测试步骤</div>
      <div class="test-item">
        <div class="test-label">请按以下步骤测试组件:</div>
        <ol>
          <li>在项目中使用 PickerAddress 组件</li>
          <li>打开浏览器开发者工具的控制台</li>
          <li>观察控制台输出的调试信息</li>
          <li>检查是否能正常显示城市列表</li>
          <li>测试搜索功能是否正常</li>
          <li>测试热门城市选择功能</li>
        </ol>
      </div>
    </div>
    
    <div class="test-section">
      <div class="section-title">📋 预期结果</div>
      <div class="test-item">
        <div class="test-label">正常情况下应该看到:</div>
        <div class="test-result">
控制台输出示例:
PickerAddress 组件已挂载
开始加载城市数据...
动态导入成功
原始城市数据类型: object
是否为数组: true
城市数据数组长度: 34
第一个省份数据: {value: 110000, text: "北京市", level: 1, pid: 0, children: [...]}
数据验证通过
开始转换数据格式...
转换后数据长度: 34
获取热门城市...
热门城市数量: 18
城市数据加载完成，共 34 个省份
=== PickerAddress 组件状态调试 ===
cityData.value: [{label: "北京市", value: 110000, ...}, ...]
cityData 长度: 34
...
=== 调试结束 ===
        </div>
      </div>
      
      <div class="test-item">
        <div class="test-label">如果出现问题，可能看到:</div>
        <div class="test-result">
控制台输出示例:
开始加载城市数据...
动态导入失败，尝试备用方法: Error: ...
使用备用方法加载城市数据...
备用方法加载的数据: [...]
或者:
备用数据加载方法也失败: Error: ...
使用最小测试数据
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <div class="section-title">🐛 故障排除</div>
      <div class="test-item">
        <div class="test-label">如果组件仍然无法显示数据:</div>
        <ol>
          <li><strong>检查控制台错误:</strong> 查看是否有JavaScript错误</li>
          <li><strong>验证数据加载:</strong> 确认控制台显示"城市数据加载完成"</li>
          <li><strong>检查组件引用:</strong> 确认 CascadeSelection 组件正确导入</li>
          <li><strong>验证数据格式:</strong> 检查 cityData.value 是否有数据</li>
          <li><strong>检查Props传递:</strong> 确认数据正确传递给 CascadeSelection</li>
        </ol>
      </div>
      
      <div class="test-item">
        <div class="test-label">常见问题解决:</div>
        <ul>
          <li><strong>数据为空:</strong> 检查 city.json 文件是否存在且格式正确</li>
          <li><strong>导入失败:</strong> 组件会自动使用备用数据</li>
          <li><strong>显示异常:</strong> 检查 CascadeSelection 组件是否正常工作</li>
          <li><strong>样式问题:</strong> 检查CSS样式是否正确加载</li>
        </ul>
      </div>
    </div>
    
    <div class="test-section">
      <div class="section-title">📞 技术支持</div>
      <div class="test-item">
        <div class="test-label">如需进一步帮助:</div>
        <p>请提供以下信息:</p>
        <ul>
          <li>完整的控制台错误信息</li>
          <li>组件使用的代码片段</li>
          <li>浏览器和版本信息</li>
          <li>项目环境信息</li>
        </ul>
      </div>
    </div>
  </div>
  
  <script>
    // 模拟控制台日志显示
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    
    function addLogEntry(message, type = 'info') {
      const logContainer = document.querySelector('.log-container');
      if (logContainer) {
        const entry = document.createElement('div');
        entry.className = `log-entry log-${type}`;
        entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logContainer.appendChild(entry);
        logContainer.scrollTop = logContainer.scrollHeight;
      }
    }
    
    console.log = function(...args) {
      originalLog.apply(console, args);
      addLogEntry(args.join(' '), 'info');
    };
    
    console.error = function(...args) {
      originalError.apply(console, args);
      addLogEntry(args.join(' '), 'error');
    };
    
    console.warn = function(...args) {
      originalWarn.apply(console, args);
      addLogEntry(args.join(' '), 'warn');
    };
    
    // 页面加载完成后的提示
    window.addEventListener('load', () => {
      console.log('测试页面加载完成，请在项目中测试 PickerAddress 组件');
    });
  </script>
</body>
</html>
