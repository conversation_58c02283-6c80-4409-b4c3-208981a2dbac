<template>
  <view class="test-page">
    <view class="header">
      <text class="title">PickerAddress 修复测试</text>
    </view>
    
    <view class="content">
      <!-- 状态信息 -->
      <view class="status-section">
        <text class="section-title">组件状态</text>
        <view class="status-item">
          <text>数据加载状态: {{ dataLoaded ? '已加载' : '未加载' }}</text>
        </view>
        <view class="status-item">
          <text>城市数据数量: {{ cityDataLength }}</text>
        </view>
        <view class="status-item">
          <text>显示搜索结果: {{ showSearchResults ? '是' : '否' }}</text>
        </view>
        <view class="status-item">
          <text>级联选择器可见: {{ !showSearchResults ? '是' : '否' }}</text>
        </view>
      </view>
      
      <!-- 控制按钮 -->
      <view class="control-section">
        <text class="section-title">调试控制</text>
        <view class="button-group">
          <button @click="debugState" class="debug-btn">调试状态</button>
          <button @click="forceShow" class="debug-btn">强制显示</button>
          <button @click="refreshData" class="debug-btn">刷新数据</button>
        </view>
      </view>
      
      <!-- 选择结果 -->
      <view class="result-section">
        <text class="section-title">选择结果</text>
        <text class="result-text">{{ selectedAddress || '未选择' }}</text>
      </view>
    </view>
    
    <!-- 城市选择器 -->
    <view class="picker-container">
      <PickerAddress
        ref="pickerRef"
        v-model:value="addressValue"
        title="选择地址"
        :show-close="false"
        :show-footer="true"
        height="400px"
        @confirm="handleConfirm"
        @change="handleChange"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { PickerAddress, type AddressInfo } from '@/components/PickerAddress'

// 响应式数据
const pickerRef = ref()
const addressValue = ref<number[]>([])
const selectedAddress = ref('')
const dataLoaded = ref(false)
const cityDataLength = ref(0)
const showSearchResults = ref(false)

// 方法
const handleConfirm = (addressInfo: AddressInfo) => {
  selectedAddress.value = addressInfo.fullAddress
  console.log('选择确认:', addressInfo)
}

const handleChange = (addressInfo: AddressInfo) => {
  selectedAddress.value = addressInfo.fullAddress
  console.log('选择改变:', addressInfo)
}

const debugState = () => {
  console.log('=== 手动调试状态 ===')
  if (pickerRef.value) {
    pickerRef.value.debugState()
    const state = pickerRef.value.getComponentState()
    console.log('组件状态:', state)
    
    // 更新显示状态
    cityDataLength.value = state.cityDataLength
    showSearchResults.value = state.showSearchResults
    dataLoaded.value = state.cityDataLength > 0
  } else {
    console.log('pickerRef 不可用')
  }
}

const forceShow = () => {
  console.log('强制显示级联选择器')
  if (pickerRef.value) {
    pickerRef.value.forceShowCascade()
    
    // 延迟更新状态
    setTimeout(() => {
      debugState()
    }, 500)
  }
}

const refreshData = () => {
  console.log('刷新数据')
  // 重新挂载组件
  const currentValue = addressValue.value
  addressValue.value = []
  
  nextTick(() => {
    addressValue.value = currentValue
    setTimeout(() => {
      debugState()
    }, 1000)
  })
}

// 生命周期
onMounted(() => {
  console.log('测试页面已挂载')
  
  // 延迟检查状态
  setTimeout(() => {
    debugState()
  }, 2000)
  
  // 定期检查状态
  setInterval(() => {
    if (pickerRef.value) {
      const state = pickerRef.value.getComponentState()
      cityDataLength.value = state.cityDataLength
      showSearchResults.value = state.showSearchResults
      dataLoaded.value = state.cityDataLength > 0
    }
  }, 3000)
})
</script>

<style scoped>
.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.content {
  margin-bottom: 20px;
}

.status-section,
.control-section,
.result-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.status-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.debug-btn {
  padding: 8px 16px;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.debug-btn:active {
  background-color: #0056cc;
}

.result-text {
  font-size: 14px;
  color: #333;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  display: block;
}

.picker-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
</style>
