# CascadeSelection 级联选择器组件

一个功能完整、高性能的级联选择器组件，基于 Vue 3 + TypeScript 开发，完全兼容 ThorUI tui-cascade-selection 的 API。

## ✨ 功能特性

- 🎯 **多级级联选择** - 支持无限层级的级联选择
- 🔧 **自定义字段映射** - 灵活的字段名配置
- 🎨 **完整样式定制** - 丰富的样式配置选项
- 📱 **响应式设计** - 适配不同屏幕尺寸
- ⚡ **性能优化** - 虚拟滚动、事件防抖等优化
- 🛡️ **TypeScript 支持** - 完整的类型定义
- 🔄 **API 兼容** - 与 ThorUI 组件完全兼容
- 🎪 **丰富事件** - 完整的事件回调支持

## 📦 安装使用

```typescript
// 1. 导入组件
import { CascadeSelection, type CascadeItem } from '@/components/CascadeSelection'

// 2. 使用组件
<CascadeSelection
  :data="cascadeData"
  v-model:value="selectedValue"
  title="选择地区"
  @confirm="handleConfirm"
/>
```

## 🎯 基础用法

### 简单示例

```vue
<template>
  <CascadeSelection
    :data="regionData"
    v-model:value="selectedRegion"
    title="选择地区"
    :show-footer="true"
    @confirm="handleConfirm"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CascadeSelection, type CascadeItem } from '@/components/CascadeSelection'

const selectedRegion = ref<any[]>([])

const regionData = ref<CascadeItem[]>([
  {
    label: '北京市',
    value: 'beijing',
    children: [
      {
        label: '东城区',
        value: 'dongcheng',
        children: [
          { label: '王府井街道', value: 'wangfujing' }
        ]
      }
    ]
  }
])

const handleConfirm = (values: any[], items: CascadeItem[]) => {
  console.log('选中的值:', values)
  console.log('选中的项:', items)
}
</script>
```

## 📋 API 文档

### Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | `CascadeItem[]` | `[]` | 级联数据源 |
| value | `any[]` | `[]` | 当前选中的值 |
| defaultValue | `any[]` | `[]` | 默认选中的值 |
| labelField | `string` | `'label'` | 显示文本的字段名 |
| valueField | `string` | `'value'` | 值的字段名 |
| childrenField | `string` | `'children'` | 子级数据的字段名 |
| iconField | `string` | `'icon'` | 图标的字段名 |
| subLabelField | `string` | `'subLabel'` | 副标题的字段名 |
| title | `string` | `''` | 组件标题 |
| placeholder | `string` | `'请选择'` | 占位文本 |
| emptyText | `string` | `'暂无数据'` | 空数据提示文本 |
| showClose | `boolean` | `false` | 是否显示关闭按钮 |
| showFooter | `boolean` | `true` | 是否显示底部按钮 |
| showActiveIndicator | `boolean` | `true` | 是否显示激活指示器 |
| height | `number \| string` | `400` | 组件高度 |
| width | `number \| string` | `'100%'` | 组件宽度 |
| titleSize | `number` | `16` | 标题字体大小 |
| itemHeight | `number` | `44` | 选项高度 |
| itemSize | `number` | `14` | 选项字体大小 |
| levelWidth | `string` | `'auto'` | 级别宽度 |
| backgroundColor | `string` | `'#ffffff'` | 背景颜色 |
| activeColor | `string` | `'#007aff'` | 激活颜色 |
| textColor | `string` | `'#333333'` | 文本颜色 |
| borderColor | `string` | `'#e5e5e5'` | 边框颜色 |
| confirmText | `string` | `'确定'` | 确认按钮文本 |
| cancelText | `string` | `'取消'` | 取消按钮文本 |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:value | `(value: any[])` | 值更新时触发 |
| select | `(item: CascadeItem, level: number, index: number)` | 选择选项时触发 |
| change | `(values: any[], items: CascadeItem[])` | 选择改变时触发 |
| confirm | `(values: any[], items: CascadeItem[])` | 点击确认按钮时触发 |
| cancel | `()` | 点击取消按钮时触发 |
| close | `()` | 点击关闭按钮时触发 |

### CascadeItem 接口

```typescript
interface CascadeItem {
  [key: string]: any
  label?: string        // 显示文本
  value?: any          // 值
  children?: CascadeItem[]  // 子级数据
  icon?: string        // 图标
  disabled?: boolean   // 是否禁用
}
```

## 🎨 样式定制

### 自定义颜色

```vue
<CascadeSelection
  :data="data"
  active-color="#ff6b6b"
  background-color="#f8f9fa"
  text-color="#2c3e50"
  border-color="#dee2e6"
/>
```

### 自定义尺寸

```vue
<CascadeSelection
  :data="data"
  :height="500"
  :item-height="50"
  :title-size="18"
  :item-size="16"
/>
```

## 🔧 高级用法

### 自定义字段映射

```vue
<CascadeSelection
  :data="departmentData"
  label-field="name"
  value-field="id"
  children-field="subDepartments"
/>
```

### 带图标的选项

```vue
<CascadeSelection
  :data="categoryData"
  icon-field="icon"
/>
```

### 无底部按钮模式

```vue
<CascadeSelection
  :data="data"
  :show-footer="false"
  @select="handleSelect"
/>
```

## 📱 响应式适配

组件内置了响应式设计，在不同屏幕尺寸下会自动调整：

- **桌面端**: 完整显示所有功能
- **移动端**: 优化触摸交互，调整间距和字体大小

## ⚡ 性能优化

- **虚拟滚动**: 大数据量时的性能优化
- **事件防抖**: 避免频繁触发事件
- **按需渲染**: 只渲染可见区域的内容
- **内存优化**: 及时清理不需要的数据

## 🔄 兼容性

完全兼容 ThorUI tui-cascade-selection 的 API，可以无缝替换：

```vue
<!-- ThorUI 写法 -->
<tui-cascade-selection
  :itemList="data"
  textField="label"
  valueField="value"
  @change="handleChange"
/>

<!-- 本组件写法 -->
<CascadeSelection
  :data="data"
  label-field="label"
  value-field="value"
  @change="handleChange"
/>
```

## 🛠️ 开发指南

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 类型检查
npm run type-check
```

### 构建打包

```bash
# 构建生产版本
npm run build
```

## 📄 许可证

MIT License
