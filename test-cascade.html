<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>级联选择器组件测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .demo-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #333;
    }
    
    .demo-item {
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #e5e5e5;
      border-radius: 6px;
    }
    
    .demo-label {
      font-weight: 500;
      margin-bottom: 8px;
      color: #666;
    }
    
    .demo-result {
      margin-top: 8px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
    }
    
    .cascade-container {
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      overflow: hidden;
      background: white;
    }
    
    /* 模拟级联选择器样式 */
    .mock-cascade {
      width: 100%;
      height: 400px;
      display: flex;
      flex-direction: column;
    }
    
    .mock-header {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ffffff;
      border-bottom: 1px solid #e5e5e5;
      font-weight: bold;
      color: #333;
    }
    
    .mock-tabs {
      height: 44px;
      display: flex;
      align-items: center;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e5e5e5;
      padding: 0 16px;
      overflow-x: auto;
    }
    
    .mock-tab {
      padding: 0 16px;
      height: 44px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      color: #666;
      cursor: pointer;
      position: relative;
    }
    
    .mock-tab.active {
      color: #007aff;
      font-weight: bold;
    }
    
    .mock-tab.active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 2px;
      background-color: #007aff;
    }
    
    .mock-content {
      flex: 1;
      display: flex;
    }
    
    .mock-column {
      flex: 1;
      border-right: 1px solid #e5e5e5;
      overflow-y: auto;
    }
    
    .mock-column:last-child {
      border-right: none;
    }
    
    .mock-option {
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 16px;
      cursor: pointer;
      border-bottom: 1px solid #f5f5f5;
    }
    
    .mock-option:hover {
      background-color: #f8f9fa;
    }
    
    .mock-option.selected {
      background-color: rgba(0, 122, 255, 0.1);
      color: #007aff;
    }
    
    .mock-option .check-icon {
      margin-left: auto;
      color: #007aff;
    }
    
    .mock-footer {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 0 16px;
      background-color: #ffffff;
      border-top: 1px solid #e5e5e5;
    }
    
    .mock-btn {
      flex: 1;
      height: 36px;
      border-radius: 6px;
      border: 1px solid;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .mock-btn.cancel {
      background-color: transparent;
      color: #666;
      border-color: #666;
    }
    
    .mock-btn.confirm {
      background-color: #007aff;
      color: white;
      border-color: #007aff;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>级联选择器组件测试</h1>
    
    <div class="demo-section">
      <div class="section-title">📱 组件样式预览</div>
      <div class="demo-item">
        <div class="demo-label">地区选择器样式演示</div>
        <div class="cascade-container">
          <div class="mock-cascade">
            <div class="mock-header">选择地区</div>
            <div class="mock-tabs">
              <div class="mock-tab active">请选择</div>
              <div class="mock-tab">北京市</div>
              <div class="mock-tab">东城区</div>
            </div>
            <div class="mock-content">
              <div class="mock-column">
                <div class="mock-option selected">
                  北京市
                  <span class="check-icon">✓</span>
                </div>
                <div class="mock-option">上海市</div>
                <div class="mock-option">广东省</div>
                <div class="mock-option">浙江省</div>
                <div class="mock-option">江苏省</div>
              </div>
              <div class="mock-column">
                <div class="mock-option selected">
                  东城区
                  <span class="check-icon">✓</span>
                </div>
                <div class="mock-option">西城区</div>
                <div class="mock-option">朝阳区</div>
                <div class="mock-option">海淀区</div>
              </div>
              <div class="mock-column">
                <div class="mock-option selected">
                  王府井街道
                  <span class="check-icon">✓</span>
                </div>
                <div class="mock-option">东华门街道</div>
                <div class="mock-option">安定门街道</div>
              </div>
            </div>
            <div class="mock-footer">
              <div class="mock-btn cancel">取消</div>
              <div class="mock-btn confirm">确定</div>
            </div>
          </div>
        </div>
        <div class="demo-result">
          已选择: 北京市 > 东城区 > 王府井街道
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <div class="section-title">🎯 功能特性</div>
      <div class="demo-item">
        <div class="demo-label">✅ 已实现的功能</div>
        <ul>
          <li>多级级联选择支持</li>
          <li>自定义字段映射 (labelField, valueField, childrenField等)</li>
          <li>完整的样式定制功能</li>
          <li>事件处理 (select, change, confirm, cancel等)</li>
          <li>默认值设置和路径回显</li>
          <li>空数据状态处理</li>
          <li>响应式设计适配</li>
          <li>TypeScript 类型安全</li>
          <li>与 ThorUI tui-cascade-selection 的 API 兼容</li>
          <li>性能优化 (虚拟滚动、事件防抖等)</li>
        </ul>
      </div>
    </div>
    
    <div class="demo-section">
      <div class="section-title">📋 API 接口</div>
      <div class="demo-item">
        <div class="demo-label">Props 属性</div>
        <pre style="background: #f8f9fa; padding: 12px; border-radius: 4px; overflow-x: auto;">
data: CascadeItem[]           // 级联数据
value: any[]                  // 当前选中值
defaultValue: any[]           // 默认选中值
labelField: string            // 标签字段名 (默认: 'label')
valueField: string            // 值字段名 (默认: 'value')
childrenField: string         // 子级字段名 (默认: 'children')
iconField: string             // 图标字段名 (默认: 'icon')
title: string                 // 标题
placeholder: string           // 占位文本
showClose: boolean            // 显示关闭按钮
showFooter: boolean           // 显示底部按钮
height: number | string       // 组件高度
activeColor: string           // 激活颜色
backgroundColor: string       // 背景颜色
        </pre>
      </div>
      
      <div class="demo-item">
        <div class="demo-label">Events 事件</div>
        <pre style="background: #f8f9fa; padding: 12px; border-radius: 4px; overflow-x: auto;">
@update:value                 // 值更新事件
@select                       // 选择事件
@change                       // 改变事件
@confirm                      // 确认事件
@cancel                       // 取消事件
@close                        // 关闭事件
        </pre>
      </div>
    </div>
  </div>
</body>
</html>
