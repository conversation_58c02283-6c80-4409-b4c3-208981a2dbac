<template>
  <view class="picker-address" :style="containerStyle">
    <!-- 搜索栏 -->
    <view v-if="showSearch" class="search-bar" :style="searchBarStyle">
      <view class="search-input-wrapper">
        <Icon name="search" :size="16" color="#999" />
        <input
          v-model="searchKeyword"
          class="search-input"
          :placeholder="searchPlaceholder"
          @input="handleSearch"
          @focus="handleSearchFocus"
          @blur="handleSearchBlur"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <Icon name="close" :size="14" color="#999" />
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view v-if="showSearchResults" class="search-results" :style="searchResultsStyle">
      <scroll-view scroll-y class="search-scroll">
        <view v-if="searchResults.length === 0" class="search-empty">
          <text class="empty-text">未找到相关城市</text>
        </view>
        <view v-else class="search-list">
          <view
            v-for="(item, index) in searchResults"
            :key="index"
            class="search-item"
            :style="searchItemStyle"
            @click="selectSearchResult(item)"
          >
            <view class="search-item-content">
              <text class="search-item-name">{{ item.label }}</text>
              <text v-if="item.provinceName || item.cityName" class="search-item-path">
                {{ getSearchItemPath(item) }}
              </text>
            </view>
            <text class="search-item-type">{{ getSearchItemType(item) }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 热门城市 -->
    <view v-if="showHotCities && !showSearchResults" class="hot-cities" :style="hotCitiesStyle">
      <text class="hot-cities-title">热门城市</text>
      <view class="hot-cities-grid">
        <view
          v-for="(city, index) in hotCities"
          :key="index"
          class="hot-city-item"
          :style="hotCityItemStyle"
          @click="selectHotCity(city)"
        >
          <text class="hot-city-name">{{ city.label }}</text>
        </view>
      </view>
    </view>

    <!-- 级联选择器 -->
    <view v-if="!showSearchResults" class="cascade-container" :style="cascadeContainerStyle">
      <CascadeSelection
        :data="cityData"
        v-model:value="selectedValues"
        :title="title"
        :placeholder="placeholder"
        :empty-text="emptyText"
        :show-close="showClose"
        :show-footer="showFooter"
        :show-active-indicator="showActiveIndicator"
        :height="cascadeHeight"
        :item-height="itemHeight"
        :active-color="activeColor"
        :background-color="backgroundColor"
        :text-color="textColor"
        :border-color="borderColor"
        :confirm-text="confirmText"
        :cancel-text="cancelText"
        label-field="label"
        value-field="value"
        children-field="children"
        @select="handleSelect"
        @change="handleChange"
        @confirm="handleConfirm"
        @cancel="handleCancel"
        @close="handleClose"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { CascadeSelection, type CascadeItem } from '@/components/CascadeSelection'
import { Icon } from '@/components/Icon'
import {
  transformCityData,
  getAddressInfo,
  findValuesByAddress,
  getHotCities,
  searchCities,
  validateCityData,
  type AddressInfo,
  type RawCityItem
} from './utils'

// Props 接口
interface Props {
  // 数据相关
  value?: number[]
  defaultValue?: number[]
  
  // 显示控制
  title?: string
  placeholder?: string
  emptyText?: string
  searchPlaceholder?: string
  showSearch?: boolean
  showHotCities?: boolean
  showClose?: boolean
  showFooter?: boolean
  showActiveIndicator?: boolean
  
  // 尺寸样式
  height?: number | string
  cascadeHeight?: number | string
  itemHeight?: number
  
  // 颜色样式
  backgroundColor?: string
  activeColor?: string
  textColor?: string
  borderColor?: string
  
  // 按钮文本
  confirmText?: string
  cancelText?: string
  
  // 其他
  customClass?: string
  customStyle?: Record<string, any>
}

// 事件接口
interface Emits {
  'update:value': [value: number[]]
  select: [addressInfo: AddressInfo, level: number]
  change: [addressInfo: AddressInfo]
  confirm: [addressInfo: AddressInfo]
  cancel: []
  close: []
  search: [keyword: string, results: CascadeItem[]]
}

// Props 默认值
const props = withDefaults(defineProps<Props>(), {
  value: () => [],
  defaultValue: () => [],
  title: '选择地区',
  placeholder: '请选择',
  emptyText: '暂无数据',
  searchPlaceholder: '搜索城市',
  showSearch: true,
  showHotCities: true,
  showClose: false,
  showFooter: true,
  showActiveIndicator: true,
  height: '60vh',
  cascadeHeight: 'auto',
  itemHeight: 44,
  backgroundColor: '#ffffff',
  activeColor: '#007aff',
  textColor: '#333333',
  borderColor: '#e5e5e5',
  confirmText: '确定',
  cancelText: '取消',
  customClass: '',
  customStyle: () => ({})
})

// 事件定义
const emit = defineEmits<Emits>()

// 响应式数据
const cityData = ref<CascadeItem[]>([])
const selectedValues = ref<number[]>([])
const searchKeyword = ref('')
const searchResults = ref<CascadeItem[]>([])
const hotCities = ref<CascadeItem[]>([])
const showSearchResults = ref(false)
const isSearchFocused = ref(false)

// 计算属性 - 样式
const containerStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  backgroundColor: props.backgroundColor,
  ...props.customStyle
}))

const searchBarStyle = computed(() => ({
  borderBottomColor: props.borderColor
}))

const searchResultsStyle = computed(() => {
  const searchBarHeight = props.showSearch ? 50 : 0
  const totalHeight = typeof props.height === 'number' ? props.height : 400
  return {
    height: `${totalHeight - searchBarHeight}px`
  }
})

const searchItemStyle = computed(() => ({
  borderBottomColor: props.borderColor
}))

const hotCitiesStyle = computed(() => ({
  borderBottomColor: props.borderColor
}))

const hotCityItemStyle = computed(() => ({
  borderColor: props.borderColor,
  color: props.textColor
}))

const cascadeContainerStyle = computed(() => {
  const searchBarHeight = props.showSearch ? 50 : 0
  const hotCitiesHeight = (props.showHotCities && hotCities.value.length > 0) ? 120 : 0
  const totalHeight = typeof props.height === 'number' ? props.height : 400
  const cascadeHeight = totalHeight - searchBarHeight - hotCitiesHeight
  
  return {
    height: props.cascadeHeight === 'auto' ? `${cascadeHeight}px` : props.cascadeHeight
  }
})

// 计算属性 - 功能
const currentAddressInfo = computed(() => {
  return getAddressInfo(cityData.value, selectedValues.value)
})

// 备用数据加载方法
const loadCityDataFallback = async () => {
  try {
    console.log('使用备用方法加载城市数据...')

    // 使用fetch加载数据
    const response = await fetch('/components/PickerAddress/city.json')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const rawCityData = await response.json()
    console.log('备用方法加载的数据:', rawCityData)

    return rawCityData
  } catch (error) {
    console.error('备用数据加载方法也失败:', error)

    // 返回最小的测试数据
    console.log('使用最小测试数据')
    return [
      {
        value: 110000,
        text: '北京市',
        level: 1,
        pid: 0,
        children: [
          {
            value: 110100,
            text: '北京市市辖区',
            level: 2,
            pid: 110000,
            children: [
              { value: 110101, text: '东城区', level: 3, pid: 110100, children: [] },
              { value: 110102, text: '西城区', level: 3, pid: 110100, children: [] },
              { value: 110105, text: '朝阳区', level: 3, pid: 110100, children: [] },
              { value: 110106, text: '丰台区', level: 3, pid: 110100, children: [] }
            ]
          }
        ]
      },
      {
        value: 120000,
        text: '天津市',
        level: 1,
        pid: 0,
        children: [
          {
            value: 120100,
            text: '天津市市辖区',
            level: 2,
            pid: 120000,
            children: [
              { value: 120101, text: '和平区', level: 3, pid: 120100, children: [] },
              { value: 120102, text: '河东区', level: 3, pid: 120100, children: [] },
              { value: 120103, text: '河西区', level: 3, pid: 120100, children: [] },
              { value: 120104, text: '南开区', level: 3, pid: 120100, children: [] }
            ]
          }
        ]
      },
      {
        value: 310000,
        text: '上海市',
        level: 1,
        pid: 0,
        children: [
          {
            value: 310100,
            text: '上海市市辖区',
            level: 2,
            pid: 310000,
            children: [
              { value: 310101, text: '黄浦区', level: 3, pid: 310100, children: [] },
              { value: 310104, text: '徐汇区', level: 3, pid: 310100, children: [] },
              { value: 310105, text: '长宁区', level: 3, pid: 310100, children: [] },
              { value: 310106, text: '静安区', level: 3, pid: 310100, children: [] }
            ]
          }
        ]
      }
    ]
  }
}

// 方法 - 数据加载
const loadCityData = async () => {
  try {
    console.log('开始加载城市数据...')

    let rawCityData: any = null

    // 尝试动态导入城市数据
    try {
      const cityModule = await import('./city.json')
      rawCityData = cityModule.default || cityModule
      console.log('动态导入成功')
    } catch (importError) {
      console.warn('动态导入失败，尝试备用方法:', importError)
      rawCityData = await loadCityDataFallback()
    }

    console.log('原始城市数据类型:', typeof rawCityData)
    console.log('是否为数组:', Array.isArray(rawCityData))

    // 检查数据是否存在
    if (!rawCityData) {
      throw new Error('城市数据为空')
    }

    // 确保数据是数组格式
    let cityArray = rawCityData
    if (!Array.isArray(rawCityData)) {
      throw new Error('城市数据格式不正确，应该是数组格式')
    }

    console.log('城市数据数组长度:', cityArray.length)
    if (cityArray.length > 0) {
      console.log('第一个省份数据:', cityArray[0])
    }

    // 验证数据格式
    if (!validateCityData(cityArray)) {
      console.warn('城市数据验证失败，但继续处理')
    }

    // 转换数据格式
    console.log('开始转换数据格式...')
    const transformedData = transformCityData(cityArray as RawCityItem[])
    console.log('转换后数据长度:', transformedData.length)

    if (transformedData.length === 0) {
      throw new Error('转换后的数据为空')
    }

    cityData.value = transformedData

    // 获取热门城市
    if (props.showHotCities) {
      console.log('获取热门城市...')
      hotCities.value = getHotCities(cityData.value)
      console.log('热门城市数量:', hotCities.value.length)
    }

    // 设置默认值
    if (props.defaultValue && props.defaultValue.length > 0) {
      console.log('设置默认值:', props.defaultValue)
      selectedValues.value = [...props.defaultValue]
    } else if (props.value && props.value.length > 0) {
      console.log('设置当前值:', props.value)
      selectedValues.value = [...props.value]
    }

    console.log('城市数据加载完成，共', cityData.value.length, '个省份')
  } catch (error: any) {
    console.error('加载城市数据失败:', error)
    console.error('错误详情:', error?.message)
    console.error('错误堆栈:', error?.stack)

    // 显示错误提示
    if (typeof uni !== 'undefined' && uni.showToast) {
      uni.showToast({
        title: '城市数据加载失败',
        icon: 'error'
      })
    } else {
      console.error('无法显示错误提示，uni对象不可用')
    }
  }
}

// 方法 - 搜索处理
const handleSearch = (e: any) => {
  const keyword = e.detail.value || e.target?.value || ''
  searchKeyword.value = keyword
  
  if (keyword.trim()) {
    searchResults.value = searchCities(cityData.value, keyword)
    showSearchResults.value = true
    emit('search', keyword, searchResults.value)
  } else {
    searchResults.value = []
    showSearchResults.value = false
  }
}

const handleSearchFocus = () => {
  isSearchFocused.value = true
  if (searchKeyword.value.trim()) {
    showSearchResults.value = true
  }
}

const handleSearchBlur = () => {
  isSearchFocused.value = false
  // 延迟隐藏搜索结果，避免点击搜索结果时被提前隐藏
  setTimeout(() => {
    if (!isSearchFocused.value) {
      showSearchResults.value = false
    }
  }, 200)
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  showSearchResults.value = false
}

const selectSearchResult = (item: CascadeItem) => {
  // 根据搜索结果类型设置选中值
  const values: number[] = []
  
  if (item.searchType === 'province') {
    values.push(item.value as number)
  } else if (item.searchType === 'city') {
    // 需要找到对应的省份
    const province = cityData.value.find((p: CascadeItem) =>
      p.children?.some((c: CascadeItem) => c.value === item.value)
    )
    if (province) {
      values.push(province.value as number, item.value as number)
    }
  } else if (item.searchType === 'district') {
    // 需要找到对应的省份和城市
    let provinceCode: number | undefined
    let cityCode: number | undefined

    cityData.value.forEach((province: CascadeItem) => {
      province.children?.forEach((city: CascadeItem) => {
        if (city.children?.some((d: CascadeItem) => d.value === item.value)) {
          provinceCode = province.value as number
          cityCode = city.value as number
        }
      })
    })
    
    if (provinceCode && cityCode) {
      values.push(provinceCode, cityCode, item.value as number)
    }
  }
  
  selectedValues.value = values
  clearSearch()
  
  // 触发选择事件
  const addressInfo = getAddressInfo(cityData.value, values)
  if (addressInfo) {
    emit('change', addressInfo)
  }
}

const selectHotCity = (city: CascadeItem) => {
  // 找到热门城市对应的省份
  const province = cityData.value.find((p: CascadeItem) =>
    p.children?.some((c: CascadeItem) => c.value === city.value)
  )
  
  if (province) {
    const values = [province.value as number, city.value as number]
    selectedValues.value = values
    
    const addressInfo = getAddressInfo(cityData.value, values)
    if (addressInfo) {
      emit('change', addressInfo)
    }
  }
}

const getSearchItemPath = (item: CascadeItem): string => {
  const parts: string[] = []
  if (item.provinceName) parts.push(item.provinceName)
  if (item.cityName) parts.push(item.cityName)
  return parts.join(' > ')
}

const getSearchItemType = (item: CascadeItem): string => {
  switch (item.searchType) {
    case 'province': return '省'
    case 'city': return '市'
    case 'district': return '区'
    default: return ''
  }
}

// 方法 - 级联选择器事件处理
const handleSelect = (_item: CascadeItem, level: number, _index: number) => {
  const addressInfo = currentAddressInfo.value
  if (addressInfo) {
    emit('select', addressInfo, level)
  }
}

const handleChange = (values: any[], _items: CascadeItem[]) => {
  selectedValues.value = values as number[]
  const addressInfo = getAddressInfo(cityData.value, values as number[])
  if (addressInfo) {
    emit('change', addressInfo)
  }
}

const handleConfirm = (values: any[], _items: CascadeItem[]) => {
  const addressInfo = getAddressInfo(cityData.value, values as number[])
  if (addressInfo) {
    emit('confirm', addressInfo)
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleClose = () => {
  emit('close')
}

// 监听器
watch(() => props.value, (newValue) => {
  if (newValue && newValue.length > 0) {
    selectedValues.value = [...newValue]
  }
}, { deep: true })

watch(selectedValues, (newValues) => {
  emit('update:value', newValues)
}, { deep: true })

// 调试方法
const debugComponentState = () => {
  console.log('=== PickerAddress 组件状态调试 ===')
  console.log('cityData.value:', cityData.value)
  console.log('cityData 长度:', cityData.value.length)
  console.log('selectedValues.value:', selectedValues.value)
  console.log('hotCities.value:', hotCities.value)
  console.log('showSearchResults.value:', showSearchResults.value)
  console.log('searchResults.value:', searchResults.value)
  console.log('currentAddressInfo.value:', currentAddressInfo.value)
  console.log('props:', props)
  console.log('=== 调试结束 ===')
}

// 生命周期
onMounted(async () => {
  console.log('PickerAddress 组件已挂载')
  await loadCityData()

  // 延迟调试，确保数据加载完成
  setTimeout(() => {
    debugComponentState()
  }, 1000)
})

// 暴露方法给父组件
defineExpose({
  getAddressInfo: () => currentAddressInfo.value,
  setAddress: (provinceName?: string, cityName?: string, districtName?: string) => {
    const values = findValuesByAddress(cityData.value, provinceName, cityName, districtName)
    selectedValues.value = values
  },
  clearSelection: () => {
    selectedValues.value = []
  },
  searchCity: (keyword: string) => {
    searchKeyword.value = keyword
    if (keyword.trim()) {
      searchResults.value = searchCities(cityData.value, keyword)
      showSearchResults.value = true
    }
  }
})
</script>

<style scoped>
.picker-address {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

/* 搜索栏样式 */
.search-bar {
  padding: 12px 16px;
  border-bottom: 1px solid;
  background-color: #ffffff;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
  gap: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #333;
  outline: none;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

/* 搜索结果样式 */
.search-results {
  flex: 1;
  overflow: hidden;
}

.search-scroll {
  height: 100%;
}

.search-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

.search-list {
  padding: 8px 0;
}

.search-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.search-item:last-child {
  border-bottom: none;
}

.search-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.search-item-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.search-item-path {
  font-size: 12px;
  color: #999;
}

.search-item-type {
  font-size: 12px;
  color: #666;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 热门城市样式 */
.hot-cities {
  padding: 16px;
  border-bottom: 1px solid;
  background-color: #fafafa;
}

.hot-cities-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  display: block;
}

.hot-cities-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hot-city-item {
  padding: 6px 12px;
  border: 1px solid;
  border-radius: 16px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
}

.hot-city-item:active {
  background-color: #f0f0f0;
}

.hot-city-name {
  font-size: 12px;
  line-height: 1;
}

/* 级联容器样式 */
.cascade-container {
  flex: 1;
  overflow: hidden;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .search-bar {
    padding: 8px 12px;
  }

  .search-input-wrapper {
    padding: 6px 10px;
  }

  .search-item {
    padding: 10px 12px;
  }

  .hot-cities {
    padding: 12px;
  }

  .hot-city-item {
    padding: 4px 8px;
  }

  .hot-city-name {
    font-size: 11px;
  }
}
</style>
