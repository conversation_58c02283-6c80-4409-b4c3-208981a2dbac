<template>
  <view class="page" :style="{ paddingTop: navBarHeight + 'px' }">
    <!-- 自定义导航栏 -->
    <CustomNavBar 
      title="城市选择器演示" 
      :show-back="true"
      background-color="#007aff"
      color="#ffffff"
    />
    
    <scroll-view scroll-y class="content" :style="{ height: `calc(100vh - ${navBarHeight}px)` }">
      <!-- 基础用法 -->
      <view class="demo-section">
        <text class="section-title">🏙️ 基础用法</text>
        
        <view class="demo-card">
          <text class="demo-label">完整模式:</text>
          <button @click="showFullPicker" class="demo-btn">选择地址</button>
          <text v-if="fullResult" class="demo-result">
            已选择: {{ fullResult }}
          </text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">简单模式:</text>
          <button @click="showSimplePicker" class="demo-btn">选择地址</button>
          <text v-if="simpleResult" class="demo-result">
            已选择: {{ simpleResult }}
          </text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">搜索模式:</text>
          <button @click="showSearchPicker" class="demo-btn">选择地址</button>
          <text v-if="searchResult" class="demo-result">
            已选择: {{ searchResult }}
          </text>
        </view>
      </view>
      
      <!-- 样式定制 -->
      <view class="demo-section">
        <text class="section-title">🎨 样式定制</text>
        
        <view class="demo-card">
          <text class="demo-label">绿色主题:</text>
          <button @click="showGreenPicker" class="demo-btn green">选择地址</button>
          <text v-if="greenResult" class="demo-result">
            已选择: {{ greenResult }}
          </text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">橙色主题:</text>
          <button @click="showOrangePicker" class="demo-btn orange">选择地址</button>
          <text v-if="orangeResult" class="demo-result">
            已选择: {{ orangeResult }}
          </text>
        </view>
      </view>
      
      <!-- 高级功能 -->
      <view class="demo-section">
        <text class="section-title">⚡ 高级功能</text>
        
        <view class="demo-card">
          <text class="demo-label">默认值设置:</text>
          <button @click="showDefaultValuePicker" class="demo-btn">选择地址</button>
          <text v-if="defaultValueResult" class="demo-result">
            已选择: {{ defaultValueResult }}
          </text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">程序化控制:</text>
          <view class="control-buttons">
            <button @click="setBeijing" class="control-btn">设置北京</button>
            <button @click="setShanghai" class="control-btn">设置上海</button>
            <button @click="clearAddress" class="control-btn">清空</button>
          </view>
          <text v-if="programResult" class="demo-result">
            当前地址: {{ programResult }}
          </text>
        </view>
      </view>
      
      <!-- 事件监听 -->
      <view class="demo-section">
        <text class="section-title">📡 事件监听</text>
        
        <view class="demo-card">
          <text class="demo-label">事件日志:</text>
          <button @click="showEventPicker" class="demo-btn">选择地址</button>
          <scroll-view v-if="eventLogs.length > 0" scroll-y class="event-logs">
            <view v-for="(log, index) in eventLogs" :key="index" class="event-log">
              <text class="event-time">{{ log.time }}</text>
              <text class="event-name">{{ log.event }}</text>
              <text class="event-data">{{ log.data }}</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 完整模式弹窗 -->
    <BottomPopup v-model:visible="fullVisible" height="70vh">
      <PickerAddress
        v-model:value="fullValue"
        title="选择地址"
        :show-close="true"
        :show-footer="true"
        @confirm="handleFullConfirm"
        @close="fullVisible = false"
        @cancel="fullVisible = false"
      />
    </BottomPopup>
    
    <!-- 简单模式弹窗 -->
    <BottomPopup v-model:visible="simpleVisible" height="400px">
      <PickerAddress
        v-model:value="simpleValue"
        title="选择地址"
        :show-search="false"
        :show-hot-cities="false"
        :show-close="true"
        :show-footer="true"
        @confirm="handleSimpleConfirm"
        @close="simpleVisible = false"
        @cancel="simpleVisible = false"
      />
    </BottomPopup>
    
    <!-- 搜索模式弹窗 -->
    <BottomPopup v-model:visible="searchVisible" height="60vh">
      <PickerAddress
        v-model:value="searchValue"
        title="搜索城市"
        :show-search="true"
        :show-hot-cities="false"
        :show-close="true"
        :show-footer="true"
        @confirm="handleSearchConfirm"
        @close="searchVisible = false"
        @cancel="searchVisible = false"
      />
    </BottomPopup>
    
    <!-- 绿色主题弹窗 -->
    <BottomPopup v-model:visible="greenVisible" height="70vh">
      <PickerAddress
        v-model:value="greenValue"
        title="选择地址"
        active-color="#52c41a"
        border-color="#d9f7be"
        :show-close="true"
        :show-footer="true"
        @confirm="handleGreenConfirm"
        @close="greenVisible = false"
        @cancel="greenVisible = false"
      />
    </BottomPopup>
    
    <!-- 橙色主题弹窗 -->
    <BottomPopup v-model:visible="orangeVisible" height="70vh">
      <PickerAddress
        v-model:value="orangeValue"
        title="选择地址"
        active-color="#fa8c16"
        border-color="#ffd591"
        :show-close="true"
        :show-footer="true"
        @confirm="handleOrangeConfirm"
        @close="orangeVisible = false"
        @cancel="orangeVisible = false"
      />
    </BottomPopup>
    
    <!-- 默认值弹窗 -->
    <BottomPopup v-model:visible="defaultValueVisible" height="70vh">
      <PickerAddress
        v-model:value="defaultValueValue"
        :default-value="[110000, 110100, 110101]"
        title="选择地址"
        :show-close="true"
        :show-footer="true"
        @confirm="handleDefaultValueConfirm"
        @close="defaultValueVisible = false"
        @cancel="defaultValueVisible = false"
      />
    </BottomPopup>
    
    <!-- 程序化控制弹窗 -->
    <BottomPopup v-model:visible="programVisible" height="70vh">
      <PickerAddress
        ref="programPickerRef"
        v-model:value="programValue"
        title="程序化控制"
        :show-close="true"
        :show-footer="true"
        @confirm="handleProgramConfirm"
        @close="programVisible = false"
        @cancel="programVisible = false"
      />
    </BottomPopup>
    
    <!-- 事件监听弹窗 -->
    <BottomPopup v-model:visible="eventVisible" height="70vh">
      <PickerAddress
        v-model:value="eventValue"
        title="事件监听"
        :show-close="true"
        :show-footer="true"
        @select="handleEventSelect"
        @change="handleEventChange"
        @confirm="handleEventConfirm"
        @cancel="handleEventCancel"
        @close="handleEventClose"
        @search="handleEventSearch"
      />
    </BottomPopup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CustomNavBar, navBarUtils } from '@/components/CustomNavBar'
import { BottomPopup } from '@/components/BottomPopup'
import { PickerAddress, pickerAddressUtils, type AddressInfo } from '@/components/PickerAddress'

// 响应式数据
const navBarHeight = ref(0)

// 基础用法状态
const fullVisible = ref(false)
const fullValue = ref<number[]>([])
const fullResult = ref('')

const simpleVisible = ref(false)
const simpleValue = ref<number[]>([])
const simpleResult = ref('')

const searchVisible = ref(false)
const searchValue = ref<number[]>([])
const searchResult = ref('')

// 样式定制状态
const greenVisible = ref(false)
const greenValue = ref<number[]>([])
const greenResult = ref('')

const orangeVisible = ref(false)
const orangeValue = ref<number[]>([])
const orangeResult = ref('')

// 高级功能状态
const defaultValueVisible = ref(false)
const defaultValueValue = ref<number[]>([])
const defaultValueResult = ref('')

const programVisible = ref(false)
const programValue = ref<number[]>([])
const programResult = ref('')
const programPickerRef = ref()

// 事件监听状态
const eventVisible = ref(false)
const eventValue = ref<number[]>([])
const eventLogs = ref<Array<{ time: string, event: string, data: string }>>([])

// 生命周期
onMounted(() => {
  navBarHeight.value = navBarUtils.getTotalHeight()
})

// 基础用法方法
const showFullPicker = () => {
  fullVisible.value = true
}

const showSimplePicker = () => {
  simpleVisible.value = true
}

const showSearchPicker = () => {
  searchVisible.value = true
}

const handleFullConfirm = (addressInfo: AddressInfo) => {
  fullResult.value = addressInfo.fullAddress
  fullVisible.value = false
}

const handleSimpleConfirm = (addressInfo: AddressInfo) => {
  simpleResult.value = addressInfo.fullAddress
  simpleVisible.value = false
}

const handleSearchConfirm = (addressInfo: AddressInfo) => {
  searchResult.value = addressInfo.fullAddress
  searchVisible.value = false
}

// 样式定制方法
const showGreenPicker = () => {
  greenVisible.value = true
}

const showOrangePicker = () => {
  orangeVisible.value = true
}

const handleGreenConfirm = (addressInfo: AddressInfo) => {
  greenResult.value = addressInfo.fullAddress
  greenVisible.value = false
}

const handleOrangeConfirm = (addressInfo: AddressInfo) => {
  orangeResult.value = addressInfo.fullAddress
  orangeVisible.value = false
}

// 高级功能方法
const showDefaultValuePicker = () => {
  defaultValueVisible.value = true
}

const handleDefaultValueConfirm = (addressInfo: AddressInfo) => {
  defaultValueResult.value = addressInfo.fullAddress
  defaultValueVisible.value = false
}

const setBeijing = () => {
  if (programPickerRef.value) {
    programPickerRef.value.setAddress('北京市', '北京市市辖区', '东城区')
    programResult.value = '北京市东城区'
  }
  programVisible.value = true
}

const setShanghai = () => {
  if (programPickerRef.value) {
    programPickerRef.value.setAddress('上海市', '上海市市辖区', '黄浦区')
    programResult.value = '上海市黄浦区'
  }
  programVisible.value = true
}

const clearAddress = () => {
  if (programPickerRef.value) {
    programPickerRef.value.clearSelection()
    programResult.value = ''
  }
  programVisible.value = true
}

const handleProgramConfirm = (addressInfo: AddressInfo) => {
  programResult.value = addressInfo.fullAddress
  programVisible.value = false
}

// 事件监听方法
const showEventPicker = () => {
  eventLogs.value = []
  eventVisible.value = true
}

const addEventLog = (event: string, data: any) => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.push({
    time,
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : String(data)
  })
}

const handleEventSelect = (addressInfo: AddressInfo, level: number) => {
  addEventLog('select', `Level ${level}: ${addressInfo.fullAddress}`)
}

const handleEventChange = (addressInfo: AddressInfo) => {
  addEventLog('change', addressInfo.fullAddress)
}

const handleEventConfirm = (addressInfo: AddressInfo) => {
  addEventLog('confirm', addressInfo.fullAddress)
  eventVisible.value = false
}

const handleEventCancel = () => {
  addEventLog('cancel', 'User cancelled')
  eventVisible.value = false
}

const handleEventClose = () => {
  addEventLog('close', 'Picker closed')
  eventVisible.value = false
}

const handleEventSearch = (keyword: string, results: any[]) => {
  addEventLog('search', `Keyword: ${keyword}, Results: ${results.length}`)
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 16px;
}

.demo-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  display: block;
}

.demo-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.demo-btn {
  width: 100%;
  height: 40px;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.demo-btn:active {
  background-color: #0056cc;
}

.demo-btn.green {
  background-color: #52c41a;
}

.demo-btn.green:active {
  background-color: #389e0d;
}

.demo-btn.orange {
  background-color: #fa8c16;
}

.demo-btn.orange:active {
  background-color: #d46b08;
}

.demo-result {
  font-size: 12px;
  color: #52c41a;
  margin-top: 8px;
  display: block;
  padding: 8px;
  background-color: #f6ffed;
  border-radius: 4px;
  border: 1px solid #d9f7be;
}

.control-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.control-btn {
  flex: 1;
  height: 32px;
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.control-btn:active {
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}

.event-logs {
  max-height: 200px;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
}

.event-log {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
}

.event-log:last-child {
  border-bottom: none;
}

.event-time {
  color: #999;
  min-width: 60px;
}

.event-name {
  color: #007aff;
  font-weight: 500;
  min-width: 60px;
}

.event-data {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .content {
    padding: 12px;
  }

  .demo-card {
    padding: 12px;
  }

  .section-title {
    font-size: 16px;
  }

  .control-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .control-btn {
    height: 36px;
  }
}
</style>
