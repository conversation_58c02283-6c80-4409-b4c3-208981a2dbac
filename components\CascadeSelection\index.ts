/**
 * 级联选择器组件
 *
 * 功能特性：
 * - 多级级联选择
 * - 自定义字段映射
 * - 样式定制
 * - 事件处理
 * - 默认值设置
 * - 空数据状态
 * - 响应式设计
 * - TypeScript 支持
 */

import CascadeSelection from './CascadeSelection.vue'
import type { CascadeItem } from './CascadeSelection.vue'

// 预设数据
export const cascadeSelectionPresets = {
  // 示例数据
  examples: {
    // 地区数据
    regions: [
      {
        label: '北京市',
        value: 'beijing',
        children: [
          {
            label: '东城区',
            value: 'dongcheng',
            children: [
              { label: '王府井街道', value: 'wangfujing' },
              { label: '东华门街道', value: 'donghuamen' },
              { label: '安定门街道', value: 'andingmen' }
            ]
          },
          {
            label: '西城区',
            value: 'xicheng',
            children: [
              { label: '西长安街街道', value: 'xichanganjie' },
              { label: '新街口街道', value: 'xinji<PERSON>ou' },
              { label: '月坛街道', value: 'yuetan' }
            ]
          },
          {
            label: '朝阳区',
            value: 'chaoyang',
            children: [
              { label: '建国门外街道', value: 'jianguomenwai' },
              { label: '朝外街道', value: 'chaowai' },
              { label: '呼家楼街道', value: 'hujialou' }
            ]
          }
        ]
      },
      {
        label: '上海市',
        value: 'shanghai',
        children: [
          {
            label: '黄浦区',
            value: 'huangpu',
            children: [
              { label: '南京东路街道', value: 'nanjingdonglu' },
              { label: '外滩街道', value: 'waitan' },
              { label: '半淞园路街道', value: 'bansonglu' }
            ]
          },
          {
            label: '徐汇区',
            value: 'xuhui',
            children: [
              { label: '湖南路街道', value: 'hunanlu' },
              { label: '天平路街道', value: 'tianpinglu' },
              { label: '枫林路街道', value: 'fenglinlu' }
            ]
          }
        ]
      },
      {
        label: '广东省',
        value: 'guangdong',
        children: [
          {
            label: '广州市',
            value: 'guangzhou',
            children: [
              { label: '越秀区', value: 'yuexiu' },
              { label: '荔湾区', value: 'liwan' },
              { label: '海珠区', value: 'haizhu' }
            ]
          },
          {
            label: '深圳市',
            value: 'shenzhen',
            children: [
              { label: '福田区', value: 'futian' },
              { label: '罗湖区', value: 'luohu' },
              { label: '南山区', value: 'nanshan' }
            ]
          }
        ]
      }
    ],

    // 分类数据
    categories: [
      {
        label: '电子产品',
        value: 'electronics',
        icon: '📱',
        children: [
          {
            label: '手机',
            value: 'phone',
            icon: '📱',
            children: [
              { label: 'iPhone', value: 'iphone', icon: '🍎' },
              { label: 'Android', value: 'android', icon: '🤖' },
              { label: '华为', value: 'huawei', icon: '📱' }
            ]
          },
          {
            label: '电脑',
            value: 'computer',
            icon: '💻',
            children: [
              { label: '笔记本', value: 'laptop', icon: '💻' },
              { label: '台式机', value: 'desktop', icon: '🖥️' },
              { label: '平板', value: 'tablet', icon: '📱' }
            ]
          }
        ]
      },
      {
        label: '服装',
        value: 'clothing',
        icon: '👕',
        children: [
          {
            label: '男装',
            value: 'mens',
            icon: '👔',
            children: [
              { label: '衬衫', value: 'shirt', icon: '👔' },
              { label: 'T恤', value: 'tshirt', icon: '👕' },
              { label: '裤子', value: 'pants', icon: '👖' }
            ]
          },
          {
            label: '女装',
            value: 'womens',
            icon: '👗',
            children: [
              { label: '连衣裙', value: 'dress', icon: '👗' },
              { label: '上衣', value: 'top', icon: '👚' },
              { label: '裙子', value: 'skirt', icon: '👗' }
            ]
          }
        ]
      }
    ],

    // 部门数据
    departments: [
      {
        name: '技术部',
        id: 'tech',
        children: [
          {
            name: '前端组',
            id: 'frontend',
            children: [
              { name: 'Vue团队', id: 'vue-team' },
              { name: 'React团队', id: 'react-team' },
              { name: 'Angular团队', id: 'angular-team' }
            ]
          },
          {
            name: '后端组',
            id: 'backend',
            children: [
              { name: 'Java团队', id: 'java-team' },
              { name: 'Python团队', id: 'python-team' },
              { name: 'Node.js团队', id: 'nodejs-team' }
            ]
          }
        ]
      },
      {
        name: '产品部',
        id: 'product',
        children: [
          {
            name: '产品设计',
            id: 'design',
            children: [
              { name: 'UI设计师', id: 'ui-designer' },
              { name: 'UX设计师', id: 'ux-designer' }
            ]
          },
          {
            name: '产品运营',
            id: 'operation',
            children: [
              { name: '用户运营', id: 'user-operation' },
              { name: '内容运营', id: 'content-operation' }
            ]
          }
        ]
      }
    ]
  },

  // 默认配置
  defaults: {
    // 基础配置
    basic: {
      labelField: 'label',
      valueField: 'value',
      childrenField: 'children',
      iconField: 'icon',
      placeholder: '请选择',
      emptyText: '暂无数据'
    },

    // 样式配置
    styles: {
      height: 400,
      backgroundColor: '#ffffff',
      activeColor: '#007aff',
      textColor: '#333333',
      borderColor: '#e5e5e5'
    },

    // 按钮配置
    buttons: {
      confirmText: '确定',
      cancelText: '取消',
      confirmButtonColor: '#007aff',
      cancelButtonColor: '#666666'
    }
  }
}

// 工具函数
export const cascadeSelectionUtils = {
  /**
   * 根据值路径查找对应的项目路径
   */
  findItemsByValues(data: CascadeItem[], values: any[], options: {
    valueField?: string
    childrenField?: string
  } = {}): CascadeItem[] {
    const {
      valueField = 'value',
      childrenField = 'children'
    } = options

    const result: CascadeItem[] = []
    let currentData = data

    for (const value of values) {
      const item = currentData.find(item => item[valueField] === value)
      if (!item) break

      result.push(item)
      currentData = item[childrenField] || []
    }

    return result
  },

  /**
   * 获取项目的完整路径标签
   */
  getItemLabels(items: CascadeItem[], labelField = 'label'): string[] {
    return items.map(item => item[labelField] || '')
  },

  /**
   * 获取项目的完整路径值
   */
  getItemValues(items: CascadeItem[], valueField = 'value'): any[] {
    return items.map(item => item[valueField])
  },

  /**
   * 验证数据格式
   */
  validateData(data: any[], options: {
    labelField?: string
    valueField?: string
    childrenField?: string
  } = {}): boolean {
    const {
      labelField = 'label',
      valueField = 'value',
      childrenField = 'children'
    } = options

    const validate = (items: any[]): boolean => {
      if (!Array.isArray(items)) return false

      return items.every(item => {
        if (typeof item !== 'object' || item === null) return false
        if (!item.hasOwnProperty(labelField) || !item.hasOwnProperty(valueField)) return false

        if (item[childrenField]) {
          return validate(item[childrenField])
        }

        return true
      })
    }

    return validate(data)
  }
}

// 类型导出
export type { CascadeItem }

// 组件导出
export { CascadeSelection }
export default CascadeSelection