<template>
  <view class="tui-cascade-selection" :class="containerClass" :style="containerStyle">
    <!-- 选择器头部 -->
    <view v-if="title || showClose" class="cascade-header" :style="headerStyle">
      <text v-if="title" class="cascade-title" :style="titleStyle">{{ title }}</text>
      <view
        v-if="showClose"
        class="cascade-close"
        @click="handleClose"
      >
        <text class="close-icon" :style="{ color: closeColor }">{{ closeText }}</text>
      </view>
    </view>

    <!-- 标签栏 -->
    <scroll-view
      :scroll-x="true"
      scroll-with-animation
      :scroll-into-view="scrollViewId"
      :style="{ backgroundColor: headerBgColor }"
      class="tui-bottom-line"
      :class="{ 'tui-btm-none': !headerLine }"
    >
      <view class="tui-selection-header" :style="{ height: tabsHeight, backgroundColor: backgroundColor }">
        <view
          class="tui-header-item"
          :class="{ 'tui-font-bold': idx === currentTab && bold }"
          :style="{
            color: idx === currentTab ? getActiveColor : tabColor,
            fontSize: tabSize + 'rpx'
          }"
          :id="`id_${idx}`"
          @tap.stop="switchNav"
          :data-current="idx"
          v-for="(item, idx) in selectedArr"
          :key="idx"
        >
          {{ item[labelField] }}
          <view
            class="tui-active-line"
            :style="{ backgroundColor: getLineColor }"
            v-if="idx === currentTab && showLine"
          ></view>
        </view>
      </view>
    </scroll-view>

    <!-- 级联选择器主体 -->
    <swiper
      class="tui-selection-list"
      :current="defTab"
      duration="300"
      @change="switchTab"
      :style="{ height: height, backgroundColor: backgroundColor }"
    >
      <swiper-item v-for="(item, index) in selectedArr" :key="index">
        <scroll-view
          scroll-y
          :scroll-into-view="item.scrollViewId"
          class="tui-selection-item"
          :style="{ height: height }"
        >
          <view class="tui-first-item" :style="{ height: firstItemTop }"></view>
          <view
            class="tui-selection-cell"
            :style="{ padding: itemPadding, backgroundColor: backgroundColor }"
            :id="`id_${subIndex}`"
            v-for="(subItem, subIndex) in item.list"
            :key="subIndex"
            @tap.stop="change(index, subIndex, subItem)"
          >
            <!-- 选中图标 -->
            <view
              v-if="item.index === subIndex && showCheckmark"
              class="tui-icon-success"
              :style="checkmarkStyle"
            >
              <text class="checkmark-icon">{{ checkmarkIcon }}</text>
            </view>

            <!-- 图片 -->
            <image
              :src="subItem[iconField]"
              v-if="subItem[iconField] && typeof subItem[iconField] === 'string' && subItem[iconField].startsWith('http')"
              class="tui-cell-img"
              :style="{
                width: imgWidth,
                height: imgHeight,
                borderRadius: imgRadius
              }"
            />

            <!-- 图标文字 -->
            <view
              v-else-if="subItem[iconField]"
              class="tui-cell-icon"
              :style="{
                width: imgWidth,
                height: imgHeight,
                borderRadius: imgRadius,
                fontSize: '24rpx',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }"
            >
              {{ subItem[iconField] }}
            </view>

            <!-- 主标题 -->
            <view
              class="tui-cell-title"
              :class="{
                'tui-font-bold': item.index === subIndex && textBold,
                'tui-flex-shrink': nowrap
              }"
              :style="{
                color: item.index === subIndex ? selectedColor : itemColor,
                fontSize: itemSize + 'rpx'
              }"
            >
              {{ subItem[labelField] }}
            </view>

            <!-- 副标题 -->
            <view
              class="tui-cell-sub_title"
              :style="{
                color: subtitleColor,
                fontSize: subtitleSize + 'rpx'
              }"
              v-if="subItem[subtitleField]"
            >
              {{ subItem[subtitleField] }}
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <!-- 底部操作区 -->
    <view v-if="showFooter" class="cascade-footer" :style="footerStyle">
      <button
        v-if="showCancel"
        class="cascade-btn cancel-btn"
        :class="cancelClass"
        :style="cancelBtnStyle"
        @click="handleCancel"
      >
        {{ cancelText }}
      </button>
      <button
        v-if="showConfirm"
        class="cascade-btn confirm-btn"
        :class="confirmClass"
        :style="confirmBtnStyle"
        @click="handleConfirm"
      >
        {{ confirmText }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'

// 级联数据项接口
interface CascadeItem {
  [key: string]: any
  children?: CascadeItem[]
}

// 级联层级接口
interface CascadeLevel {
  data: CascadeItem[]
  selectedIndex: number
}

// Props 接口
interface Props {
  // 基础属性
  data?: CascadeItem[]
  value?: any[]
  defaultValue?: any[]

  // 字段配置
  labelField?: string
  valueField?: string
  keyField?: string
  childrenField?: string
  iconField?: string
  subtitleField?: string

  // 显示配置
  title?: string
  showClose?: boolean
  closeText?: string
  closeColor?: string
  showArrow?: boolean
  arrowIcon?: string
  showCheckmark?: boolean
  checkmarkIcon?: string
  emptyText?: string

  // ThorUI 兼容属性
  headerLine?: boolean
  headerBgColor?: string
  tabsHeight?: string
  tabColor?: string
  tabSize?: number
  activeColor?: string
  bold?: boolean
  showLine?: boolean
  lineColor?: string
  checkMarkSize?: number
  checkMarkColor?: string
  imgWidth?: string
  imgHeight?: string
  imgRadius?: string
  textBold?: boolean
  nowrap?: boolean
  firstItemTop?: string

  // 样式配置
  height?: string
  backgroundColor?: string

  // 头部样式
  titleColor?: string
  titleSize?: string | number
  headerPadding?: string

  // 级联列样式
  levelWidth?: string | number
  levelBorderColor?: string

  // 选项样式
  itemHeight?: string | number
  itemPadding?: string
  itemColor?: string
  itemSize?: number
  selectedColor?: string
  selectedBgColor?: string
  hoverBgColor?: string
  subtitleColor?: string
  subtitleSize?: number

  // 图标样式
  iconColor?: string
  iconSize?: string | number
  arrowColor?: string
  checkmarkColor?: string

  // 底部按钮
  showFooter?: boolean
  showCancel?: boolean
  showConfirm?: boolean
  cancelText?: string
  confirmText?: string
  cancelColor?: string
  confirmColor?: string
  cancelBgColor?: string
  confirmBgColor?: string

  // 功能配置
  multiple?: boolean
  checkStrictly?: boolean

  // 其他
  customClass?: string
  customStyle?: Record<string, any>
}

// 事件接口
interface Emits {
  'update:value': [value: any[]]
  change: [value: any[], selectedItems: CascadeItem[]]
  select: [item: CascadeItem, level: number, index: number]
  confirm: [value: any[], selectedItems: CascadeItem[]]
  cancel: []
  close: []
}

// Props 默认值
const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  value: () => [],
  defaultValue: () => [],
  labelField: 'label',
  valueField: 'value',
  keyField: 'key',
  childrenField: 'children',
  iconField: 'icon',
  subtitleField: 'subtitle',
  title: '',
  showClose: false,
  closeText: '✕',
  closeColor: '#999999',
  showArrow: true,
  arrowIcon: '>',
  showCheckmark: true,
  checkmarkIcon: '✓',
  emptyText: '暂无数据',

  // ThorUI 兼容属性
  headerLine: true,
  headerBgColor: '#FFFFFF',
  tabsHeight: '88rpx',
  tabColor: '#555',
  tabSize: 28,
  activeColor: '',
  bold: true,
  showLine: true,
  lineColor: '',
  checkMarkSize: 15,
  checkMarkColor: '',
  imgWidth: '40rpx',
  imgHeight: '40rpx',
  imgRadius: '50%',
  textBold: true,
  nowrap: false,
  firstItemTop: '20rpx',

  height: '300px',
  backgroundColor: '#FFFFFF',
  titleColor: '#333333',
  titleSize: 18,
  headerPadding: '15px 20px',
  levelWidth: '33.33%',
  levelBorderColor: '#f0f0f0',
  itemHeight: 50,
  itemPadding: '20rpx 30rpx',
  itemColor: '#333',
  itemSize: 28,
  selectedColor: '#333',
  selectedBgColor: '#f0f8ff',
  hoverBgColor: '#f8f9fa',
  subtitleColor: '#999',
  subtitleSize: 24,
  iconColor: '#666666',
  iconSize: 16,
  arrowColor: '#cccccc',
  checkmarkColor: '#007aff',
  showFooter: false,
  showCancel: true,
  showConfirm: true,
  cancelText: '取消',
  confirmText: '确定',
  cancelColor: '#666666',
  confirmColor: '#ffffff',
  cancelBgColor: '#f8f9fa',
  confirmBgColor: '#007aff',
  multiple: false,
  checkStrictly: false,
  customClass: '',
  customStyle: () => ({})
})

// 定义事件
const emit = defineEmits<Emits>()

// 响应式数据
// ThorUI 兼容数据
const currentTab = ref<number>(0)
const defTab = ref<number>(0)
const scrollViewId = ref<string>('id__1')
const selectedArr = ref<any[]>([])

// 计算属性 - ThorUI 兼容
const getActiveColor = computed(() => {
  return props.activeColor || '#5677fc'
})

const getLineColor = computed(() => {
  return props.lineColor || '#5677fc'
})

// 计算属性
const containerClass = computed(() => [
  'tui-cascade-selection-base',
  props.customClass
])

const containerStyle = computed(() => ({
  height: props.height,
  backgroundColor: props.backgroundColor,
  ...props.customStyle
}))

const headerStyle = computed(() => ({
  padding: props.headerPadding,
  borderBottom: `1px solid ${props.levelBorderColor}`
}))

const titleStyle = computed(() => ({
  color: props.titleColor,
  fontSize: typeof props.titleSize === 'number' ? `${props.titleSize}px` : props.titleSize,
  fontWeight: 'bold'
}))

const closeStyle = computed(() => ({
  color: props.closeColor
}))

const footerStyle = computed(() => ({
  display: 'flex' as const,
  gap: '12px',
  padding: '15px 20px',
  borderTop: `1px solid ${props.levelBorderColor}`
}))

const checkmarkStyle = computed(() => ({
  color: getCkMarkColor.value,
  fontSize: props.checkMarkSize + 'rpx'
}))

const cancelClass = computed(() => [
  'btn-base',
  'btn-cancel'
])

const confirmClass = computed(() => [
  'btn-base',
  'btn-confirm'
])

const cancelBtnStyle = computed(() => ({
  color: props.cancelColor,
  backgroundColor: props.cancelBgColor,
  fontSize: props.itemSize + 'rpx'
}))

const confirmBtnStyle = computed(() => ({
  color: props.confirmColor,
  backgroundColor: props.confirmBgColor,
  fontSize: props.itemSize + 'rpx'
}))

const getCkMarkColor = computed(() => {
  return props.checkMarkColor || getActiveColor.value
})

// 方法
const initializeLevels = () => {
  selectedArr.value = []
  currentTab.value = 0
  defTab.value = 0

  if (props.data.length > 0) {
    selectedArr.value.push({
      list: props.data,
      index: -1,
      scrollViewId: 'id__1'
    })

    // 如果有默认值，设置选中路径
    if (props.value.length > 0 || props.defaultValue.length > 0) {
      const initialValue = props.value.length > 0 ? props.value : props.defaultValue
      setSelectedPath(initialValue)
    }
  }
}

// ThorUI 兼容方法
const switchNav = (e: any) => {
  const current = e.currentTarget.dataset.current
  if (current !== currentTab.value) {
    currentTab.value = current
    defTab.value = current
  }
}

const switchTab = (e: any) => {
  const current = e.detail.current
  currentTab.value = current
  defTab.value = current
}

const change = (index: number, subIndex: number, subItem: CascadeItem) => {
  // 更新选中状态
  selectedArr.value[index].index = subIndex

  // 清除后续级别
  selectedArr.value = selectedArr.value.slice(0, index + 1)

  // 如果有子级，添加子级
  const children = subItem[props.childrenField]
  if (children && children.length > 0) {
    selectedArr.value.push({
      list: children,
      index: -1,
      scrollViewId: `id__${index + 2}`
    })

    // 切换到下一个标签
    currentTab.value = index + 1
    defTab.value = index + 1
  }

  // 发射事件
  emit('select', subItem, index, subIndex)

  const selectedItems = getSelectedItems()
  const values = selectedItems.map(item => item[props.valueField])

  emit('update:value', values)
  emit('change', values, selectedItems)
}



const setSelectedPath = (values: any[]) => {
  selectedArr.value = []
  currentTab.value = 0

  if (props.data.length === 0) return

  let currentData = props.data
  selectedArr.value.push({
    list: currentData,
    index: -1,
    scrollViewId: 'id__1'
  })

  for (let i = 0; i < values.length; i++) {
    const value = values[i]
    const index = currentData.findIndex(item => item[props.valueField] === value)

    if (index !== -1) {
      selectedArr.value[i].index = index

      const selectedItem = currentData[index]
      const children = selectedItem[props.childrenField]

      if (children && children.length > 0 && i < values.length - 1) {
        currentData = children
        selectedArr.value.push({
          list: currentData,
          index: -1,
          scrollViewId: `id__${i + 2}`
        })
        currentTab.value = i + 1
      }
    } else {
      break
    }
  }
}



const getSelectedItems = (): CascadeItem[] => {
  const items: CascadeItem[] = []

  for (let i = 0; i < selectedArr.value.length; i++) {
    const level = selectedArr.value[i]
    if (level.index !== -1 && level.list[level.index]) {
      items.push(level.list[level.index])
    }
  }

  return items
}

const handleClose = () => {
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
}

const handleConfirm = () => {
  const selectedItems = getSelectedItems()
  const values = selectedItems.map(item => item[props.valueField])
  emit('confirm', values, selectedItems)
}

// 监听数据变化
watch(() => props.data, () => {
  initializeLevels()
}, { immediate: true, deep: true })

watch(() => props.value, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(getSelectedItems().map(item => item[props.valueField]))) {
    setSelectedPath(newValue)
  }
}, { deep: true })

// 暴露方法
defineExpose({
  getSelectedItems,
  setSelectedPath,
  reset: initializeLevels
})
</script>

<style scoped>
/* ThorUI 级联选择器样式 */
.tui-cascade-selection {
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 头部样式 */
.cascade-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.cascade-title {
  text-align: center;
  line-height: 1.4;
  font-weight: 600;
}

.cascade-close {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.cascade-close:hover {
  background-color: #f0f0f0;
}

.close-icon {
  font-size: 16px;
  line-height: 1;
}

/* 标签栏样式 */
.tui-bottom-line {
  border-bottom: 1rpx solid #EEEEEE;
}

.tui-btm-none {
  border-bottom: none !important;
}

.tui-selection-header {
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: 0 30rpx;
}

.tui-header-item {
  position: relative;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;
  cursor: pointer;
}

.tui-font-bold {
  font-weight: bold;
}

.tui-active-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 主体内容样式 */
.tui-selection-list {
  flex: 1;
}

.tui-selection-item {
  height: 100%;
}

.tui-first-item {
  width: 100%;
}

.tui-selection-cell {
  display: flex;
  align-items: center;
  position: relative;
  border-bottom: 1rpx solid #EEEEEE;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.tui-selection-cell:hover {
  background-color: #f8f9fa;
}

.tui-selection-cell:active {
  background-color: #f0f0f0;
}

.tui-selection-cell:last-child {
  border-bottom: none;
}

/* 选中图标 */
.tui-icon-success {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.checkmark-icon {
  font-size: 30rpx;
  color: #5677fc;
  line-height: 1;
}

/* 图片样式 */
.tui-cell-img {
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 文本样式 */
.tui-cell-title {
  flex: 1;
  line-height: 1.4;
  word-break: break-word;
  transition: color 0.2s ease;
}

.tui-flex-shrink {
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tui-cell-sub_title {
  margin-top: 8rpx;
  line-height: 1.3;
  word-break: break-word;
}

/* 底部按钮样式 */
.cascade-footer {
  flex-shrink: 0;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
  padding: 15px 20px;
}

.cascade-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  font-weight: 500;
  outline: none;
}

.btn-cancel {
  border: 1px solid #e5e5e5;
  background-color: #ffffff;
  color: #666666;
}

.btn-cancel:hover {
  background-color: #f8f9fa;
  border-color: #d0d0d0;
}

.btn-cancel:active {
  background-color: #f0f0f0;
  transform: scale(0.98);
}

.btn-confirm {
  background-color: #5677fc;
  color: #ffffff;
  border: 1px solid #5677fc;
}

.btn-confirm:hover {
  background-color: #4a6cf7;
  border-color: #4a6cf7;
}

.btn-confirm:active {
  background-color: #3f5af0;
  transform: scale(0.98);
}

/* 滚动条样式 */
.tui-selection-item::-webkit-scrollbar {
  width: 4px;
}

.tui-selection-item::-webkit-scrollbar-track {
  background: transparent;
}

.tui-selection-item::-webkit-scrollbar-thumb {
  background: #e5e5e5;
  border-radius: 2px;
}

.tui-selection-item::-webkit-scrollbar-thumb:hover {
  background: #cccccc;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .tui-cascade-selection {
    background-color: #2a2a2a;
    color: #ffffff;
  }

  .cascade-header {
    background-color: #1f1f1f;
    border-bottom-color: #444444;
  }

  .cascade-title {
    color: #ffffff;
  }

  .cascade-close:hover {
    background-color: #444444;
  }

  .tui-bottom-line {
    border-bottom-color: #444444;
  }

  .tui-selection-header {
    background-color: #2a2a2a;
  }

  .tui-header-item {
    color: #cccccc;
  }

  .tui-selection-cell {
    background-color: #2a2a2a;
    border-bottom-color: #444444;
  }

  .tui-selection-cell:hover {
    background-color: #3a3a3a;
  }

  .tui-selection-cell:active {
    background-color: #444444;
  }

  .tui-cell-title {
    color: #ffffff;
  }

  .tui-cell-sub_title {
    color: #cccccc;
  }

  .checkmark-icon {
    color: #409eff;
  }

  .cascade-footer {
    background-color: #1f1f1f;
    border-top-color: #444444;
  }

  .btn-cancel {
    background-color: #3a3a3a;
    border-color: #555555;
    color: #cccccc;
  }

  .btn-cancel:hover {
    background-color: #444444;
    border-color: #666666;
  }

  .btn-cancel:active {
    background-color: #555555;
  }

  .btn-confirm {
    background-color: #409eff;
    border-color: #409eff;
  }

  .btn-confirm:hover {
    background-color: #337ecc;
    border-color: #337ecc;
  }

  .btn-confirm:active {
    background-color: #2b6cb0;
  }

  .tui-selection-item::-webkit-scrollbar-thumb {
    background: #555555;
  }

  .tui-selection-item::-webkit-scrollbar-thumb:hover {
    background: #666666;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .tui-cascade-selection {
    height: 100vh;
  }

  .cascade-header,
  .cascade-footer {
    padding-left: 15px;
    padding-right: 15px;
  }

  .cascade-footer {
    flex-direction: column;
    gap: 8px;
  }

  .cascade-btn {
    width: 100%;
  }

  .tui-selection-cell {
    padding: 30rpx;
  }

  .tui-cell-title {
    font-size: 32rpx;
  }

  .tui-cell-sub_title {
    font-size: 28rpx;
  }

  .tui-header-item {
    padding: 0 15rpx;
    font-size: 26rpx;
  }
}

/* 无障碍访问支持 */
@media (prefers-reduced-motion: reduce) {
  .tui-selection-cell,
  .cascade-close,
  .cascade-btn,
  .tui-header-item,
  .tui-active-line {
    transition: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .tui-selection-cell {
    border-bottom-width: 2px;
  }

  .tui-bottom-line {
    border-bottom-width: 2px;
  }

  .btn-cancel,
  .btn-confirm {
    border-width: 2px;
  }

  .tui-active-line {
    height: 8rpx;
  }
}

/* 滚动条样式 */
.cascade-level::-webkit-scrollbar {
  width: 4px;
}

.cascade-level::-webkit-scrollbar-track {
  background: transparent;
}

.cascade-level::-webkit-scrollbar-thumb {
  background: #e5e5e5;
  border-radius: 2px;
}

.cascade-level::-webkit-scrollbar-thumb:hover {
  background: #cccccc;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .cascade-selection-container {
    background-color: #2a2a2a;
    color: #ffffff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  }

  .cascade-header {
    background-color: #1f1f1f;
    border-bottom-color: #444444;
  }

  .cascade-footer {
    background-color: #1f1f1f;
    border-top-color: #444444;
  }

  .cascade-title {
    color: #ffffff;
  }

  .cascade-body,
  .cascade-level {
    background-color: #2a2a2a;
  }

  .cascade-level:not(:last-child) {
    border-right-color: #444444;
  }

  .cascade-item {
    border-bottom-color: #444444;
  }

  .cascade-item:hover {
    background-color: #3a3a3a;
  }

  .cascade-item:active,
  .cascade-item.item-touch-active {
    background-color: #444444;
  }

  .cascade-item.item-selected {
    background-color: #1f2937;
  }

  .cascade-item.item-selected::before {
    background-color: #409eff;
  }

  .item-text {
    color: #ffffff;
  }

  .item-subtitle {
    color: #cccccc;
  }

  .arrow-icon {
    color: #666666;
  }

  .item-has-children .arrow-icon {
    color: #999999;
  }

  .checkmark-icon {
    color: #409eff;
  }

  .cascade-close:hover {
    background-color: #444444;
  }

  .cascade-close:active {
    background-color: #555555;
  }

  .btn-cancel {
    background-color: #3a3a3a;
    border-color: #555555;
    color: #cccccc;
  }

  .btn-cancel:hover {
    background-color: #444444;
    border-color: #666666;
  }

  .btn-cancel:active {
    background-color: #555555;
  }

  .btn-confirm {
    background-color: #409eff;
    border-color: #409eff;
  }

  .btn-confirm:hover {
    background-color: #337ecc;
    border-color: #337ecc;
  }

  .btn-confirm:active {
    background-color: #2b6cb0;
  }

  .cascade-level::-webkit-scrollbar-thumb {
    background: #555555;
  }

  .cascade-level::-webkit-scrollbar-thumb:hover {
    background: #666666;
  }

  .empty-text {
    color: #666666;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .cascade-selection-container {
    height: 100vh;
    border-radius: 0;
  }

  .cascade-header,
  .cascade-footer {
    padding-left: 15px;
    padding-right: 15px;
  }

  .cascade-footer {
    flex-direction: column;
    gap: 8px;
  }

  .cascade-btn {
    width: 100%;
    margin-bottom: 0;
  }

  .cascade-item {
    min-height: 56px;
    padding: 0 16px;
  }

  .item-text {
    font-size: 17px;
  }

  .item-subtitle {
    font-size: 15px;
  }
}

/* 无障碍访问支持 */
@media (prefers-reduced-motion: reduce) {
  .cascade-item,
  .cascade-close,
  .cascade-btn,
  .arrow-icon {
    transition: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .cascade-item {
    border-bottom: 2px solid #000000;
  }

  .cascade-item.item-selected::before {
    width: 5px;
  }

  .btn-cancel {
    border-width: 2px;
  }

  .btn-confirm {
    border-width: 2px;
  }
}

/* 滚动条样式 */
.cascade-level::-webkit-scrollbar {
  width: 4px;
}

.cascade-level::-webkit-scrollbar-track {
  background: transparent;
}

.cascade-level::-webkit-scrollbar-thumb {
  background: #e5e5e5;
  border-radius: 2px;
}

.cascade-level::-webkit-scrollbar-thumb:hover {
  background: #cccccc;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .cascade-selection-container {
    background-color: #2a2a2a;
    color: #ffffff;
  }
  
  .cascade-header {
    border-bottom-color: #444444;
  }
  
  .cascade-footer {
    border-top-color: #444444;
  }
  
  .cascade-title {
    color: #ffffff;
  }
  
  .cascade-item {
    border-bottom-color: #444444;
  }
  
  .cascade-item:hover {
    background-color: #3a3a3a;
  }
  
  .item-text {
    color: #cccccc;
  }
  
  .cascade-close:hover {
    background-color: #444444;
  }
  
  .btn-cancel {
    background-color: #3a3a3a;
    border-color: #555555;
    color: #cccccc;
  }
  
  .btn-cancel:hover {
    background-color: #444444;
  }
  
  .cascade-level::-webkit-scrollbar-thumb {
    background: #555555;
  }
  
  .cascade-level::-webkit-scrollbar-thumb:hover {
    background: #666666;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .cascade-selection-container {
    height: 100vh;
    border-radius: 0;
  }
  
  .cascade-header,
  .cascade-footer {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .cascade-footer {
    flex-direction: column;
  }
  
  .cascade-btn {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .cascade-btn:last-child {
    margin-bottom: 0;
  }
}
</style>
