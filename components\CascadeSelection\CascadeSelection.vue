<template>
  <view class="cascade-selection" :style="containerStyle">
    <!-- 标题栏 -->
    <view v-if="title || showClose" class="cascade-header" :style="headerStyle">
      <view class="header-left">
        <view v-if="showClose" class="close-btn" @click="handleClose">
          <Icon name="close" :size="closeIconSize" :color="closeIconColor" />
        </view>
      </view>
      <view class="header-title" :style="titleStyle">{{ title }}</view>
      <view class="header-right"></view>
    </view>

    <!-- 级联选择区域 -->
    <view class="cascade-content" :style="contentStyle">
      <!-- 标签栏 -->
      <scroll-view
        v-if="levels.length > 0"
        :scroll-x="true"
        scroll-with-animation
        :scroll-into-view="scrollViewId"
        class="cascade-tabs"
        :style="tabsStyle"
      >
        <view class="tabs-container">
          <view
            v-for="(level, index) in levels"
            :key="index"
            :id="`tab_${index}`"
            class="tab-item"
            :class="{ 'tab-active': index === currentLevel }"
            :style="getTabStyle(index)"
            @click="switchLevel(index)"
          >
            <text class="tab-text">{{ level.label || placeholder }}</text>
            <view
              v-if="index === currentLevel && showActiveIndicator"
              class="tab-indicator"
              :style="indicatorStyle"
            ></view>
          </view>
        </view>
      </scroll-view>

      <!-- 选项列表 -->
      <swiper
        class="cascade-swiper"
        :current="currentLevel"
        :duration="300"
        @change="onSwiperChange"
        :style="swiperStyle"
      >
        <swiper-item
          v-for="(level, levelIndex) in levels"
          :key="levelIndex"
          class="swiper-item"
        >
          <scroll-view
            scroll-y
            class="options-scroll"
            :style="scrollStyle"
            :scroll-into-view="level.scrollIntoView"
          >
            <!-- 空状态 -->
            <view v-if="!level.options || level.options.length === 0" class="empty-state">
              <text class="empty-text">{{ emptyText }}</text>
            </view>

            <!-- 选项列表 -->
            <view v-else class="options-list">
              <view
                v-for="(option, optionIndex) in level.options"
                :key="optionIndex"
                :id="`option_${levelIndex}_${optionIndex}`"
                class="option-item"
                :class="{ 'option-selected': level.selectedIndex === optionIndex }"
                :style="getOptionStyle(level.selectedIndex === optionIndex)"
                @click="selectOption(levelIndex, optionIndex, option)"
              >
                <!-- 图标 -->
                <view v-if="option[iconField]" class="option-icon">
                  <text class="icon-text">{{ option[iconField] }}</text>
                </view>

                <!-- 内容 -->
                <view class="option-content">
                  <text class="option-label">{{ option[labelField] }}</text>
                  <text v-if="option[subLabelField]" class="option-sublabel">
                    {{ option[subLabelField] }}
                  </text>
                </view>

                <!-- 选中标识 -->
                <view v-if="level.selectedIndex === optionIndex" class="option-check">
                  <Icon name="check" :size="checkIconSize" :color="checkIconColor" />
                </view>
              </view>
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 底部按钮 -->
    <view v-if="showFooter" class="cascade-footer" :style="footerStyle">
      <view class="footer-buttons">
        <button
          class="btn btn-cancel"
          :style="cancelButtonStyle"
          @click="handleCancel"
        >
          {{ cancelText }}
        </button>
        <button
          class="btn btn-confirm"
          :style="confirmButtonStyle"
          :disabled="!canConfirm"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { Icon } from '@/components/Icon'

// 类型定义
export interface CascadeItem {
  [key: string]: any
  label?: string
  value?: any
  children?: CascadeItem[]
  icon?: string
  disabled?: boolean
}

interface CascadeLevel {
  label: string
  value: any
  options: CascadeItem[]
  selectedIndex: number
  scrollIntoView: string
}

// Props 接口
interface Props {
  // 数据相关
  data?: CascadeItem[]
  value?: any[]
  defaultValue?: any[]

  // 字段映射
  labelField?: string
  valueField?: string
  childrenField?: string
  iconField?: string
  subLabelField?: string

  // 显示控制
  title?: string
  placeholder?: string
  emptyText?: string
  showClose?: boolean
  showFooter?: boolean
  showActiveIndicator?: boolean

  // 尺寸样式
  height?: number | string
  width?: number | string
  titleSize?: number
  itemHeight?: number
  itemSize?: number
  levelWidth?: string

  // 颜色样式
  backgroundColor?: string
  headerBackgroundColor?: string
  footerBackgroundColor?: string
  tabBackgroundColor?: string
  activeColor?: string
  textColor?: string
  subTextColor?: string
  borderColor?: string
  checkIconColor?: string
  closeIconColor?: string

  // 按钮样式
  confirmText?: string
  cancelText?: string
  confirmButtonColor?: string
  cancelButtonColor?: string

  // 图标尺寸
  checkIconSize?: number
  closeIconSize?: number

  // 其他
  customClass?: string
  customStyle?: Record<string, any>
}

// 事件接口
interface Emits {
  'update:value': [value: any[]]
  select: [item: CascadeItem, level: number, index: number]
  change: [values: any[], items: CascadeItem[]]
  confirm: [values: any[], items: CascadeItem[]]
  cancel: []
  close: []
}

// Props 默认值
const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  value: () => [],
  defaultValue: () => [],
  labelField: 'label',
  valueField: 'value',
  childrenField: 'children',
  iconField: 'icon',
  subLabelField: 'subLabel',
  title: '',
  placeholder: '请选择',
  emptyText: '暂无数据',
  showClose: false,
  showFooter: true,
  showActiveIndicator: true,
  height: 400,
  width: '100%',
  titleSize: 16,
  itemHeight: 44,
  itemSize: 14,
  levelWidth: 'auto',
  backgroundColor: '#ffffff',
  headerBackgroundColor: '#ffffff',
  footerBackgroundColor: '#ffffff',
  tabBackgroundColor: '#f8f9fa',
  activeColor: '#007aff',
  textColor: '#333333',
  subTextColor: '#999999',
  borderColor: '#e5e5e5',
  checkIconColor: '#007aff',
  closeIconColor: '#666666',
  confirmText: '确定',
  cancelText: '取消',
  confirmButtonColor: '#007aff',
  cancelButtonColor: '#666666',
  checkIconSize: 16,
  closeIconSize: 18,
  customClass: '',
  customStyle: () => ({})
})

// 事件定义
const emit = defineEmits<Emits>()

// 响应式数据
const currentLevel = ref(0)
const levels = ref<CascadeLevel[]>([])
const scrollViewId = ref('')
const selectedPath = ref<CascadeItem[]>([])

// 计算属性 - 样式
const containerStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  backgroundColor: props.backgroundColor,
  ...props.customStyle
}))

const headerStyle = computed(() => ({
  backgroundColor: props.headerBackgroundColor,
  borderBottomColor: props.borderColor
}))

const titleStyle = computed(() => ({
  fontSize: `${props.titleSize}px`,
  color: props.textColor
}))

const contentStyle = computed(() => {
  const headerHeight = (props.title || props.showClose) ? 44 : 0
  const footerHeight = props.showFooter ? 60 : 0
  const contentHeight = (typeof props.height === 'number' ? props.height : 400) - headerHeight - footerHeight

  return {
    height: `${contentHeight}px`
  }
})

const tabsStyle = computed(() => ({
  backgroundColor: props.tabBackgroundColor,
  borderBottomColor: props.borderColor
}))

const swiperStyle = computed(() => {
  const tabsHeight = levels.value.length > 0 ? 44 : 0
  const contentHeight = parseInt(contentStyle.value.height) - tabsHeight

  return {
    height: `${contentHeight}px`
  }
})

const scrollStyle = computed(() => ({
  height: '100%'
}))

const footerStyle = computed(() => ({
  backgroundColor: props.footerBackgroundColor,
  borderTopColor: props.borderColor
}))

const indicatorStyle = computed(() => ({
  backgroundColor: props.activeColor
}))

const cancelButtonStyle = computed(() => ({
  color: props.cancelButtonColor,
  borderColor: props.cancelButtonColor
}))

const confirmButtonStyle = computed(() => ({
  backgroundColor: props.confirmButtonColor,
  borderColor: props.confirmButtonColor
}))

// 计算属性 - 功能
const canConfirm = computed(() => {
  return selectedPath.value.length > 0
})

// 方法 - 样式
const getTabStyle = (index: number) => {
  const isActive = index === currentLevel.value
  return {
    color: isActive ? props.activeColor : props.textColor,
    fontWeight: isActive ? 'bold' : 'normal'
  }
}

const getOptionStyle = (isSelected: boolean) => {
  return {
    height: `${props.itemHeight}px`,
    backgroundColor: isSelected ? `${props.activeColor}10` : 'transparent',
    color: isSelected ? props.activeColor : props.textColor
  }
}

// 方法 - 数据处理
const initializeLevels = () => {
  levels.value = []
  selectedPath.value = []

  if (!props.data || props.data.length === 0) {
    return
  }

  // 创建第一级
  const firstLevel: CascadeLevel = {
    label: props.placeholder,
    value: undefined,
    options: props.data,
    selectedIndex: -1,
    scrollIntoView: ''
  }

  levels.value.push(firstLevel)
  currentLevel.value = 0

  // 如果有默认值，设置默认选中
  if (props.defaultValue && props.defaultValue.length > 0) {
    setDefaultValue()
  } else if (props.value && props.value.length > 0) {
    setValue(props.value)
  }
}

const setDefaultValue = () => {
  if (!props.defaultValue || props.defaultValue.length === 0) return

  let currentOptions = props.data
  let currentLevelIndex = 0

  for (const defaultVal of props.defaultValue) {
    if (!currentOptions) break

    const optionIndex = currentOptions.findIndex(option =>
      option[props.valueField] === defaultVal
    )

    if (optionIndex === -1) break

    const selectedOption = currentOptions[optionIndex]

    // 更新当前级别的选中状态
    if (levels.value[currentLevelIndex]) {
      levels.value[currentLevelIndex].selectedIndex = optionIndex
      levels.value[currentLevelIndex].label = selectedOption[props.labelField]
      levels.value[currentLevelIndex].value = selectedOption[props.valueField]
    }

    selectedPath.value.push(selectedOption)

    // 如果有子级，创建下一级
    if (selectedOption[props.childrenField] && selectedOption[props.childrenField].length > 0) {
      currentLevelIndex++
      const nextLevel: CascadeLevel = {
        label: props.placeholder,
        value: undefined,
        options: selectedOption[props.childrenField],
        selectedIndex: -1,
        scrollIntoView: ''
      }

      if (!levels.value[currentLevelIndex]) {
        levels.value.push(nextLevel)
      } else {
        levels.value[currentLevelIndex] = nextLevel
      }

      currentOptions = selectedOption[props.childrenField]
    } else {
      break
    }
  }

  // 设置当前级别为最后一个有选项的级别
  currentLevel.value = Math.min(currentLevelIndex, levels.value.length - 1)
}

const setValue = (values: any[]) => {
  if (!values || values.length === 0) return

  let currentOptions = props.data
  let currentLevelIndex = 0

  selectedPath.value = []

  for (const value of values) {
    if (!currentOptions) break

    const optionIndex = currentOptions.findIndex(option =>
      option[props.valueField] === value
    )

    if (optionIndex === -1) break

    const selectedOption = currentOptions[optionIndex]

    // 更新当前级别的选中状态
    if (levels.value[currentLevelIndex]) {
      levels.value[currentLevelIndex].selectedIndex = optionIndex
      levels.value[currentLevelIndex].label = selectedOption[props.labelField]
      levels.value[currentLevelIndex].value = selectedOption[props.valueField]
    }

    selectedPath.value.push(selectedOption)

    // 如果有子级，创建下一级
    if (selectedOption[props.childrenField] && selectedOption[props.childrenField].length > 0) {
      currentLevelIndex++
      const nextLevel: CascadeLevel = {
        label: props.placeholder,
        value: undefined,
        options: selectedOption[props.childrenField],
        selectedIndex: -1,
        scrollIntoView: ''
      }

      if (!levels.value[currentLevelIndex]) {
        levels.value.push(nextLevel)
      } else {
        levels.value[currentLevelIndex] = nextLevel
      }

      currentOptions = selectedOption[props.childrenField]
    } else {
      break
    }
  }

  // 设置当前级别为最后一个有选项的级别
  currentLevel.value = Math.min(currentLevelIndex, levels.value.length - 1)
}

// 方法 - 交互处理
const switchLevel = (index: number) => {
  if (index >= 0 && index < levels.value.length) {
    currentLevel.value = index
    updateScrollView()
  }
}

const onSwiperChange = (e: any) => {
  currentLevel.value = e.detail.current
  updateScrollView()
}

const selectOption = (levelIndex: number, optionIndex: number, option: CascadeItem) => {
  if (option.disabled) return

  const level = levels.value[levelIndex]
  if (!level) return

  // 更新选中状态
  level.selectedIndex = optionIndex
  level.label = option[props.labelField]
  level.value = option[props.valueField]

  // 更新选中路径
  selectedPath.value = selectedPath.value.slice(0, levelIndex)
  selectedPath.value.push(option)

  // 移除后续级别
  levels.value = levels.value.slice(0, levelIndex + 1)

  // 如果有子级，创建下一级
  if (option[props.childrenField] && option[props.childrenField].length > 0) {
    const nextLevel: CascadeLevel = {
      label: props.placeholder,
      value: undefined,
      options: option[props.childrenField],
      selectedIndex: -1,
      scrollIntoView: ''
    }

    levels.value.push(nextLevel)

    // 自动切换到下一级
    nextTick(() => {
      currentLevel.value = levelIndex + 1
      updateScrollView()
    })
  }

  // 发射事件
  emit('select', option, levelIndex, optionIndex)

  const values = selectedPath.value.map(item => item[props.valueField])
  emit('update:value', values)
  emit('change', values, selectedPath.value)

  // 如果没有子级且不显示底部按钮，自动确认
  if ((!option[props.childrenField] || option[props.childrenField].length === 0) && !props.showFooter) {
    handleConfirm()
  }
}

const updateScrollView = () => {
  // 更新标签栏滚动位置
  if (currentLevel.value > 1) {
    scrollViewId.value = `tab_${Math.max(0, currentLevel.value - 1)}`
  } else {
    scrollViewId.value = 'tab_0'
  }

  // 更新选项列表滚动位置
  const level = levels.value[currentLevel.value]
  if (level && level.selectedIndex >= 0) {
    nextTick(() => {
      const scrollIndex = Math.max(0, level.selectedIndex - 2)
      level.scrollIntoView = `option_${currentLevel.value}_${scrollIndex}`
    })
  }
}

// 方法 - 事件处理
const handleClose = () => {
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
}

const handleConfirm = () => {
  const values = selectedPath.value.map(item => item[props.valueField])
  emit('confirm', values, selectedPath.value)
}

// 监听器
watch(() => props.data, () => {
  initializeLevels()
}, { deep: true, immediate: true })

watch(() => props.value, (newValue) => {
  if (newValue && newValue.length > 0) {
    setValue(newValue)
  }
}, { deep: true })

watch(() => props.defaultValue, () => {
  if (props.defaultValue && props.defaultValue.length > 0) {
    setDefaultValue()
  }
}, { deep: true })

watch(currentLevel, () => {
  updateScrollView()
})

// 生命周期
onMounted(() => {
  initializeLevels()
})
</script>

<style scoped>
.cascade-selection {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 标题栏样式 */
.cascade-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  border-bottom: 1px solid;
  position: relative;
}

.header-left,
.header-right {
  width: 60px;
  display: flex;
  align-items: center;
}

.header-title {
  flex: 1;
  text-align: center;
  font-weight: bold;
  line-height: 1;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 内容区域样式 */
.cascade-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标签栏样式 */
.cascade-tabs {
  border-bottom: 1px solid;
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
}

.tab-item {
  position: relative;
  padding: 0 16px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: color 0.2s;
  white-space: nowrap;
  min-width: 80px;
}

.tab-text {
  font-size: 14px;
  line-height: 1;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  border-radius: 1px;
}

/* Swiper 样式 */
.cascade-swiper {
  flex: 1;
  overflow: hidden;
}

.swiper-item {
  height: 100%;
}

.options-scroll {
  height: 100%;
}

/* 选项列表样式 */
.options-list {
  padding: 8px 0;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
  margin: 0 8px;
}

.option-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.option-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon-text {
  font-size: 16px;
  line-height: 1;
}

.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.option-label {
  font-size: 14px;
  line-height: 1.4;
}

.option-sublabel {
  font-size: 12px;
  line-height: 1.2;
  margin-top: 2px;
  opacity: 0.7;
}

.option-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  color: #999999;
}

.empty-text {
  font-size: 14px;
}

/* 底部按钮样式 */
.cascade-footer {
  border-top: 1px solid;
  padding: 12px 16px;
}

.footer-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  flex: 1;
  height: 36px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: transparent;
}

.btn-cancel:active {
  opacity: 0.7;
}

.btn-confirm {
  color: white;
}

.btn-confirm:active {
  opacity: 0.8;
}

.btn-confirm:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .cascade-header {
    height: 40px;
    padding: 0 12px;
  }

  .header-title {
    font-size: 16px;
  }

  .tabs-container {
    height: 40px;
    padding: 0 12px;
  }

  .tab-item {
    height: 40px;
    padding: 0 12px;
    min-width: 60px;
  }

  .option-item {
    padding: 0 12px;
    margin: 0 4px;
  }

  .cascade-footer {
    padding: 8px 12px;
  }
}
</style>